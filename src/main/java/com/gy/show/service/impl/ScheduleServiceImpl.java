package com.gy.show.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cetc10.spaceflight.orbitpre.orbit.LLAPredict;
import com.gy.show.common.ServiceException;
import com.gy.show.constants.Constants;
import com.gy.show.entity.dos.*;
import com.gy.show.entity.dto.*;
import com.gy.show.entity.dto.external.RespScheduleDTO;
import com.gy.show.entity.dto.external.TerminalConfigDTO;
import com.gy.show.entity.dto.external.TerminalMachineDTO;
import com.gy.show.enums.AlgorithmContextEnum;
import com.gy.show.enums.EquipmentEnum;
import com.gy.show.enums.TaskScheduleStatusEnum;
import com.gy.show.enums.WebSocketTypeEnum;
import com.gy.show.mapper.CommonMapper;
import com.gy.show.service.*;
import com.gy.show.strategy.algo.AbstractAlgorithmTemplate;
import com.gy.show.strategy.algo.AlgorithmContext;
import com.gy.show.util.DateUtil;
import com.gy.show.util.GISUtils;
import com.gy.show.ws.GlobalServer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.locationtech.jts.geom.*;
import org.locationtech.jts.geom.util.GeometryCombiner;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ScheduleServiceImpl implements ScheduleService {

    @Autowired
    private DataEquipmentOccupancyService dataEquipmentOccupancyService;

    @Autowired
    private DataGeneralService dataGeneralService;

    @Autowired
    private RequirementTaskService requirementTaskService;

    @Autowired
    private TaskTargetService taskTargetService;

    @Autowired
    private RequirementTargetTrackService requirementTargetTrackService;

    @Autowired
    private AlgorithmContext algorithmContext;

    @Autowired
    private CommonMapper commonMapper;

    @Autowired
    private DataAreaService dataAreaService;

    @Autowired
    private TaskTargetRelationService taskTargetRelationService;

    @Autowired
    private SatelliteService satelliteService;

    @Autowired
    private GlobalServer globalServer;

    @Autowired
    private ScheduleLogService scheduleLogService;

    @Autowired
    private ExternalDataService externalDataService;

    @Autowired
    private TerminalInfoService terminalInfoService;

    @Autowired
    private ThreadPoolTaskScheduler scheduler;

    @Autowired
    private TerminalDataServiceImpl terminalDataService;

    @Autowired
    private SysDataMappingService dataMappingService;

    private GeometryFactory geometryFactory = new GeometryFactory();

    @Override
    public void confirmSchedule(List<ConfirmScheduleDTO> confirmScheduleDTOs) {
        if (CollUtil.isNotEmpty(confirmScheduleDTOs)) {
            RequirementTask task = requirementTaskService.getById(confirmScheduleDTOs.get(0).getTaskId());

            List<RequirementTask> tasks = requirementTaskService.list(Wrappers.<RequirementTask>lambdaQuery().eq(RequirementTask::getRequirementId, task.getRequirementId()));
            List<String> taskIds = tasks.stream()
                    .map(RequirementTask::getId)
                    .collect(Collectors.toList());

            // 删除原有数据
            for (String taskId : taskIds) {
                dataEquipmentOccupancyService.deleteByTaskId(taskId);
            }
        }

        for (ConfirmScheduleDTO confirmScheduleDTO : confirmScheduleDTOs) {
            // 根据任务ID查询任务下所有的目标轨迹
//            Map<String, List<RequirementTargetTrack>> tracks = queryTargetTrackByTask(confirmScheduleDTO.getTaskId());

            // 计算每个设备对于对于航迹的开始时间和结束时间
//            calculateTrackTime(tracks, confirmScheduleDTO);

            // 首先验证资源分配时间是否被占用，不然插入两条数据影响后面流程
//            checkEquipmentUsageTime(confirmScheduleDTO);

            // 插入资源设备占用表
            insertEquipmentOccupancy(confirmScheduleDTO);

            // 插入目标设备任务关联表
            saveOrUpdateTaskRelation(confirmScheduleDTO);

            // 更新终端使用状态
            updateTerminalStatus(confirmScheduleDTO.getTaskId());

            // 添加定时任务
            scheduleTask(confirmScheduleDTO);
        }

        // 向操控端发送结果
        send2Control(confirmScheduleDTOs);
    }

    private void send2Control(List<ConfirmScheduleDTO> confirmScheduleDTOs) {
        try {
            externalDataService.packageScheduleResult(confirmScheduleDTOs);
            log.info("向操控端发送数据成功");
        } catch (Exception e) {
            log.error("向操控端发送数据失败", e);
        }
    }

    private void scheduleTask(ConfirmScheduleDTO confirmScheduleDTO) {
        List<ConfirmScheduleDTO.EquipmentDTO> equipments = confirmScheduleDTO.getEquipments();

        for (ConfirmScheduleDTO.EquipmentDTO equipment : equipments) {
            LocalDateTime startTime = equipment.getStartTime();
            LocalDateTime endTime = equipment.getEndTime();
            if (startTime.isBefore(LocalDateTime.now())) {
                log.error("设备：{}的开始时间：{}小于当前时间，无法执行定时下发指令", equipment.getName(), startTime);
                continue;
            }

            // 加入定时任务 - 定时向站点发送开消息
            scheduler.schedule(() -> sendCommand(confirmScheduleDTO.getTaskId(), equipment, 1), Date.from(startTime.atZone(ZoneId.systemDefault()).toInstant()));
            log.info("添加定时任务成功，任务将在：{}执行打开任务", startTime);

            scheduler.schedule(() -> sendCommand(confirmScheduleDTO.getTaskId(), equipment, 2), Date.from(endTime.atZone(ZoneId.systemDefault()).toInstant()));
            log.info("添加定时任务成功，任务将在：{}执行关闭任务", endTime);
        }
    }

    /**
     *
     * @param taskId
     * @param equipment
     * @param state 1 打开站点 2 关闭站点
     */
    private void sendCommand(String taskId, ConfirmScheduleDTO.EquipmentDTO equipment, Integer state) {
        try {
            // 根据设备类型给不同站发送遥控开关指令
            stationScheduleOpen(state, equipment);

            if (state == 1) {
                // 给终端发送体制切换指令
                sendTerminalControl(taskId, equipment);
            }
        } catch (Exception e) {
            log.error("执行站点切换或终端切换命令失败", e);
        }
    }

    private void stationScheduleOpen(Integer state, ConfirmScheduleDTO.EquipmentDTO equipment) {
        log.info("【定时任务】开始执行站点切换");
        // 通过节点id获取映射的key
        List<SysDataMapping> dataMappings = dataMappingService.queryDataMapping(Collections.singletonList(2));

        List<SysDataMapping> mappingList = dataMappings.stream()
                .filter(dm -> dm.getDataValue().contains(equipment.getEquipmentId() + ""))
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(mappingList)) {
            log.warn("该站点未配置映射表，无需执行站点切换");
            return;
        }

        SysDataMapping dataMapping = mappingList.get(0);

        // 打开对应站点
        switch (dataMapping.getDataKey()) {
            case "_ka":
            case "_s":
                // 打开对应站点
                terminalDataService.switchSpaceStation(dataMapping.getDataKey(), state == 1 ? 1 : 2);
                break;
            case "_missile":
                terminalDataService.switchMissileStation(state == 1 ? 6 : 7);
                break;
            case "_uav":
                terminalDataService.switchUavStation(state == 1 ? 0x31 : 0x30);
                break;
        }
    }

    private void sendTerminalControl(String taskId, ConfirmScheduleDTO.EquipmentDTO equipment) {
        log.info("【定时任务】开始执行终端体制切换");
        String nodeId = equipment.getEquipmentId();

        // 从常量中获取当前站点的终端配置
        List<TerminalConfigDTO> terminalConfigDTOS = Constants.terminalConfig.stream()
                .filter(t -> t.getNodeId().equals(nodeId))
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(terminalConfigDTOS)) {
            log.error("当前站点未配置终端体制");
        }

        // 根据任务ID获取终端编号
        String tarId = getTerminalNoByTaskId(taskId);

        // 进入这个判断说明该目标未关联终端
        if (StringUtils.isBlank(tarId)) {
            log.warn("该目标未关联终端,无需执行终端体制切换");
            return;
        }

        TerminalConfigDTO configDTO = terminalConfigDTOS.get(0);

        TerminalMachineDTO terminalMachineDTO = new TerminalMachineDTO();

        terminalMachineDTO.setTargetNode(Integer.parseInt(tarId));
        terminalMachineDTO.setRxFreq((int) configDTO.getRxFreq());
        terminalMachineDTO.setTxFreq((int) configDTO.getTxFreq());
        terminalMachineDTO.setTxAtte((int) configDTO.getTxAtte());

        // 发送终端切换控制指令
        terminalDataService.sendMachineParams(terminalMachineDTO);
    }

    private String getTerminalNoByTaskId(String taskId) {
        RequirementTask task = requirementTaskService.getById(taskId);

        TaskTargetRelation taskTargetRelation = taskTargetRelationService.getById(task.getTargetRelationId());

        // 通过目标ID去查询映射表
        SysDataMapping sysDataMapping = dataMappingService.getByDataValue(taskTargetRelation.getTargetId());

        return sysDataMapping != null ? sysDataMapping.getDataKey() : null;
    }

    private void updateTerminalStatus(String taskId) {
        List<TaskTargetRelation> targetRelations = taskTargetRelationService.list(Wrappers.<TaskTargetRelation>lambdaQuery()
                .eq(TaskTargetRelation::getTaskId, taskId)
                .eq(TaskTargetRelation::getType, 1));

        if (CollUtil.isEmpty(targetRelations)) return;

        List<String> values = targetRelations.stream()
                .map(t -> t.getGeneralId() + "_" + t.getTargetId())
                .collect(Collectors.toList());

        List<TerminalInfo> terminalInfos = terminalInfoService.list(Wrappers.<TerminalInfo>lambdaQuery().in(TerminalInfo::getBindTargetId, values));

        if (CollUtil.isNotEmpty(terminalInfos)) {
            terminalInfos.stream()
                    .forEach(t -> t.setTerminalStatus(1));

            terminalInfoService.updateBatchById(terminalInfos);
        }
    }

    private void saveOrUpdateTaskRelation(ConfirmScheduleDTO confirmScheduleDTO) {
        taskTargetRelationService.remove(Wrappers.<TaskTargetRelation>lambdaQuery().eq(TaskTargetRelation::getTaskId, confirmScheduleDTO.getTaskId())
                .eq(TaskTargetRelation::getType, 2));

        List<TaskTargetRelation> relations = new ArrayList<>();
        for (ConfirmScheduleDTO.EquipmentDTO equipment : confirmScheduleDTO.getEquipments()) {
            DataGeneral dataGeneral = dataGeneralService.getById(equipment.getGeneralId());
            Map<String, Object> equip = commonMapper.getOne(dataGeneral.getTableName(), equipment.getEquipmentId());
            TaskTargetRelation relation = new TaskTargetRelation();

            BeanUtils.copyProperties(equipment, relation);

            relation.setTaskId(confirmScheduleDTO.getTaskId());
            relation.setTargetId(equipment.getEquipmentId());
            relation.setType(2);// 2表示是设备
            relation.setLongitude((BigDecimal) equip.get("longitude"));
            relation.setLatitude((BigDecimal) equip.get("latitude"));
            relation.setAltitude((BigDecimal) equip.get("altitude"));
            relation.setName((String) equip.get("name"));

            relations.add(relation);
        }

        taskTargetRelationService.saveBatch(relations);
    }

    private void calculateTrackTime(Map<String, List<RequirementTargetTrack>> tracks, ConfirmScheduleDTO confirmScheduleDTO) {
        List<ConfirmScheduleDTO.EquipmentDTO> equipments = confirmScheduleDTO.getEquipments();

        Map<String, List<ConfirmScheduleDTO.EquipmentDTO>> equipmentsGroup = equipments.stream()
                .collect(Collectors.groupingBy(ConfirmScheduleDTO.EquipmentDTO::getEquipmentId));

        RequirementTask task = requirementTaskService.getById(confirmScheduleDTO.getTaskId());
        // 绘制航迹
        Map<String, LineString> lines = buildTrackMap(tracks);

        List<String> generalIds = equipments.stream()
                .map(ConfirmScheduleDTO.EquipmentDTO::getGeneralId)
                .collect(Collectors.toList());

        Collection<DataGeneral> dataGenerals = dataGeneralService.listByIds(generalIds);

        Map<String, List<DataGeneral>> groupByGeneral = dataGenerals.stream()
                .collect(Collectors.groupingBy(DataGeneral::getId));

        Map<String, List<Map<String, Object>>> params = new HashMap<>();
        for (ConfirmScheduleDTO.EquipmentDTO equipment : equipments) {
            List<DataGeneral> generals = groupByGeneral.get(equipment.getGeneralId());
            Map<String, Object> eq = commonMapper.getOne(generals.get(0).getTableName(), equipment.getEquipmentId());

            String key = generals.get(0).getDataType().toString();
            List<Map<String, Object>> maps = params.get(key);
            if (CollUtil.isNotEmpty(maps)) {
                maps.add(eq);
            } else {
                List<Map<String, Object>> values = new ArrayList<>();
                values.add(eq);
                params.put(key, values);
            }
        }

        // 绘制区域
        buildPolygon(params);

        for (Map.Entry<String, LineString> entry : lines.entrySet()) {
            LineString line = entry.getValue();
            List<RequirementTargetTrack> flightPath = tracks.get(entry.getKey());

            // 转换，将航迹转换成坐标类，方便后续计算
            CoordinateWithTimeDTO[] coordinates = convertCoordinate(flightPath, task);

            // 这里二次循环是航迹对每个设备来计算进出时间
            for (Map.Entry<String, List<Map<String, Object>>> area : params.entrySet()) {
                List<Map<String, Object>> areas = area.getValue();
                for (Map<String, Object> map : areas) {
                    Polygon circle = (Polygon) map.get("polygon");
                    // 找到交点并计算进出时间

                    Instant entryTime, exitTime;
                    if (circle != null && line.intersects(circle)) {
                        Geometry intersection = line.intersection(circle);

                        if (intersection instanceof LineString) {
                            LineString intersectionLine = (LineString) intersection;
                            Coordinate start = intersectionLine.getStartPoint().getCoordinate();
                            Coordinate end = intersectionLine.getEndPoint().getCoordinate();

                            // 计算最终的进出时间
                            entryTime = GISUtils.interpolateTime(coordinates, start);
                            exitTime = GISUtils.interpolateTime(coordinates, end);

                            // 处理环线情况：当起始点和结束点相同时（如环线与区域相切）
                            if (start.equals2D(end) && entryTime != null && entryTime.equals(exitTime)) {
                                log.info("检测到环线相切情况，起始点和结束点相同: {}", start);
                            }

                            // 将数据放置到对应类，后续好保存相关数据
                            List<ConfirmScheduleDTO.EquipmentDTO> equipmentDTOS = equipmentsGroup.get(map.get("id").toString());
                            ConfirmScheduleDTO.EquipmentDTO equipmentDTO = equipmentDTOS.get(0);

                            equipmentDTO.setStartTime(LocalDateTime.ofInstant(entryTime, ZoneId.systemDefault()));
                            equipmentDTO.setEndTime(LocalDateTime.ofInstant(exitTime, ZoneId.systemDefault()));

                            log.info("Entry time: {}", entryTime);
                            log.info("Exit time:  {}", exitTime);
                        } else if (intersection instanceof MultiLineString) {
                            MultiLineString multiLineString = (MultiLineString) intersection;
                            for (int i = 0; i < multiLineString.getNumGeometries(); i++) {
                                LineString intersectionLine = (LineString) multiLineString.getGeometryN(i);

                                Coordinate start = intersectionLine.getStartPoint().getCoordinate();
                                Coordinate end = intersectionLine.getEndPoint().getCoordinate();

                                // 计算最终的进出时间
                                entryTime = GISUtils.interpolateTime(coordinates, start);
                                exitTime = GISUtils.interpolateTime(coordinates, end);

                                // 处理环线情况：当起始点和结束点相同时（如环线与区域相切）
                                if (start.equals2D(end) && entryTime != null && entryTime.equals(exitTime)) {
                                    log.info("检测到环线相切情况，起始点和结束点相同: {}", start);
                                }

                                // 将数据放置到对应类，后续好保存相关数据
                                List<ConfirmScheduleDTO.EquipmentDTO> equipmentDTOS = equipmentsGroup.get(map.get("id").toString());
                                ConfirmScheduleDTO.EquipmentDTO equipmentDTO = equipmentDTOS.get(0);

                                // 计算时间
                                LocalDateTime startTime = equipmentDTO.getStartTime();
                                LocalDateTime entryDate = entryTime != null ? LocalDateTime.ofInstant(entryTime, ZoneId.of("UTC+8")) : null;
                                LocalDateTime earlyDate = (startTime != null && startTime.isBefore(entryDate)) ? startTime : entryDate;

                                LocalDateTime endTime = equipmentDTO.getEndTime();
                                LocalDateTime exitDate = exitTime != null ? LocalDateTime.ofInstant(exitTime, ZoneId.of("UTC+8")) : null;
                                LocalDateTime laterDate = (endTime != null && endTime.isAfter(exitDate)) ? endTime : exitDate;

                                equipmentDTO.setStartTime(earlyDate);
                                equipmentDTO.setEndTime(laterDate);

                                log.info("MultiLineString Entry time: {}", earlyDate);
                                log.info("MultiLineString Exit time:  {}", laterDate);
                            }
                        } else {
                            log.error("Intersection is not a line string");
                        }
                    } else {
                        List<ConfirmScheduleDTO.EquipmentDTO> equipmentDTOS = equipmentsGroup.get(map.get("id").toString());
                        ConfirmScheduleDTO.EquipmentDTO equipmentDTO = equipmentDTOS.get(0);
                        if (equipmentDTO.getStartTime() == null || equipmentDTO.getEndTime() == null) {
                            equipmentDTO.setStartTime(task.getStartTime());
                            equipmentDTO.setEndTime(task.getEndTime());
                            log.warn("设备覆盖区域没有覆盖航迹或不支持覆盖计算，设备占用时间取任务的开始和结束时间, e:{}", equipmentDTO);
                        }
                    }

                }
            }
        }
    }

    private Map<String, List<RequirementTargetTrack>> queryTargetTrackByTask(String taskId) {
        TaskTargetRelationDTO relationDTOS = taskTargetService.listByTask(taskId, true);

//        List<String> relationIds = relationDTOS.stream()
//                .map(TaskTargetRelationDTO::getId)
//                .collect(Collectors.toList());

        List<RequirementTargetTrackDTO> targetTrack = relationDTOS.getTargetTrack();

//        List<RequirementTargetTrack> tracks = requirementTargetTrackService.queryTrackByIds(relationIds);

        // 根据relationId进行分组，每组表示一个目标以及航迹数据
//        Map<String, List<RequirementTargetTrack>> groupByRelation = tracks.stream()
//                .collect(Collectors.groupingBy(RequirementTargetTrack::getRelationId));

        Map<String, List<RequirementTargetTrack>> groupByRelation = targetTrack.stream()
                .map(RequirementTargetTrackDTO::convert)
                .collect(Collectors.groupingBy(RequirementTargetTrack::getRelationId));

        return groupByRelation;
    }

    @Override
    public Map<String, List<Map<String, Object>>> invokeAlgorithm(AlgorithmDTO algorithmDTO, IPage page) {
        List<DataArea> areas;
        // 查询所有域
        if (StringUtils.isNotBlank(algorithmDTO.getAreaId())) {
            DataArea dataArea = dataAreaService.getById(algorithmDTO.getAreaId());
            areas = Collections.singletonList(dataArea);
        } else {
            areas = dataAreaService.list();
        }

        Map<String, List<Map<String, Object>>> equipments;
        // 查询可用资源,按当前页面加载的资源进行查询
        if (CollUtil.isNotEmpty(algorithmDTO.getEquipments())) {
            equipments = algorithmDTO.getEquipments().stream()
                    .collect(Collectors.groupingBy(m -> m.get("dataType").toString()));
        } else {
            List<Map<String, List<Map<String, Object>>>> mergeList = new ArrayList<>();
            for (DataArea area : areas) {
                // 这里默认查询全部
                Page<Object> p = new Page<>(-1, -1);
                Map<String, IPage<Map<String, Object>>> sources = dataGeneralService.queryIdleSource(p, area.getId());

                Map<String, List<Map<String, Object>>> temp = sources.entrySet()
                        .stream()
                        .collect(Collectors.toMap(
                                Map.Entry::getKey,
                                entry -> entry.getValue().getRecords()
                        ));

                mergeList.add(temp);
            }

            // 合并map
            equipments = mergeList.stream()
                    .flatMap(map -> map.entrySet().stream())
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            Map.Entry::getValue,
                            (list1, list2) -> {
                                List<Map<String, Object>> mergedList = new ArrayList<>(list1);
                                mergedList.addAll(list2);
                                return mergedList;
                            }
                    ));
        }


        // 查询目标轨迹
        List<RequirementTask> tasks = requirementTaskService.list(Wrappers.<RequirementTask>lambdaQuery()
                .in(RequirementTask::getId, algorithmDTO.getTaskIds()));

        List<String> relationIds = tasks.stream()
                .map(RequirementTask::getTargetRelationId)
                .collect(Collectors.toList());

        // 查询任务关联目标的ID
//        List<TaskTargetRelation> targetRelations = taskTargetService.list(Wrappers.<TaskTargetRelation>lambdaQuery()
//                .in(TaskTargetRelation::getTaskId, taskIds));

//        List<String> relationIds = targetRelations.stream()
//                .map(TaskTargetRelation::getId)
//                .collect(Collectors.toList());

        List<RequirementTargetTrack> tracks = requirementTargetTrackService.list(Wrappers.<RequirementTargetTrack>lambdaQuery()
                .in(RequirementTargetTrack::getRelationId, relationIds));

        // 根据relationId分组，也就是根据目标进行分组
        Map<String, List<RequirementTargetTrack>> groupByTarget = tracks.stream()
                .collect(Collectors.groupingBy(RequirementTargetTrack::getRelationId));

        // 调用推荐算法,需要定义入参，目标轨迹、可用资源列表、使用算法
        InvokeAlgorithmDTO algo = new InvokeAlgorithmDTO();
        algo.setTracks(groupByTarget);
        algo.setEquipments(equipments);
        algo.setPriorityList(algorithmDTO.getPriorityList());

        AbstractAlgorithmTemplate instance = algorithmContext.getInstance(AlgorithmContextEnum.getEnumByCode(algorithmDTO.getAlgo()));

        if (instance == null) {
            log.info("当前算法暂未支持:{}，请输入正确的算法类型", algorithmDTO.getAlgo());
            throw new ServiceException("当前算法暂未支持，请输入正确的算法类型");
        }

        Map<String, List<Map<String, Object>>> result = new HashMap<>();
//        Map<String, List<RequirementTask>> groupByTask = tasks.stream()
//                .collect(Collectors.groupingBy(RequirementTask::getTargetRelationId));
//
//        algo.setTasks(groupByTask);
//        algo.setTracks(groupByTarget);

        Map<String, InvokeAlgorithmDTO> dtoMap = new HashMap<>();
        for (RequirementTask task : tasks) {
            algo.setTask(task);
            Map<String, List<RequirementTargetTrack>> track = new HashMap<>();
            track.put(task.getTargetRelationId(), groupByTarget.get(task.getTargetRelationId()));

            algo.setTracks(track);
            Map<String, List<Map<String, Object>>> algoResult = instance.invokeBasicAlgorithm(algo);
            log.info("调度算法返回结果:{}", algoResult);

            result.putAll(algoResult);

            InvokeAlgorithmDTO algoCopy = new InvokeAlgorithmDTO();
            BeanUtil.copyProperties(algo, algoCopy);
            dtoMap.put(task. getId(), algoCopy);
        }

        if (!algorithmDTO.getAlgo().equals(AlgorithmContextEnum.CAVER_CALCULATE.getCode())) {
            // 根据任务优先级和设备的可测控目标数进行资源重新分配
            Map<String, List<Map<String, Object>>> newResult = resourceAllocation(result);

            // 计算总覆盖率
            calculateTotalCoverTime0(newResult, dtoMap);

            int count = 0;
            for (Map.Entry<String, List<Map<String, Object>>> entry : newResult.entrySet()) {
                List<Map<String, Object>> marked = entry.getValue().stream()
                        .filter(m -> m.get("marked").toString().equals("1"))
                        .collect(Collectors.toList());
                if (marked.size() > 0) continue;

                count++;
            }
            log.info("未分配任务数量{}", count);
            return newResult;
        } else {
            return result;
        }
    }

    private void calculateTotalCoverTime0(Map<String, List<Map<String, Object>>> result, Map<String, InvokeAlgorithmDTO> dtoMap) {
        for (Map.Entry<String, List<Map<String, Object>>> entry : result.entrySet()) {
            String taskId = entry.getKey();

            InvokeAlgorithmDTO algorithmDTO = dtoMap.get(taskId);
            RequirementTask task = requirementTaskService.getById(taskId);
            List<Map<String, Object>> equipments = entry.getValue();
            if (equipments.size() == 0) continue;

            List<Polygon> polygons = new ArrayList<>();
            // 过滤出被标记的设备
            for (Map<String, Object> equipment : equipments) {
                if (equipment.get("marked").equals(1)) {
                    polygons.add((Polygon) equipment.get("polygon"));
                }
                equipment.put("polygon", null);
            }
            if (polygons.size() == 0) continue;

            // 合并区域
            Geometry combinePolygon = GeometryCombiner.combine(polygons).union();

            Map<String, LineString> lineStringMap = algorithmDTO.getLineStringMap();
            LineString lineString = lineStringMap.get(task.getTargetRelationId());

            Geometry intersection = lineString.intersection(combinePolygon);

            double totalCover = intersection.getLength() / lineString.getLength();

            equipments.get(0).put("totalCover", totalCover);
        }
    }


//    private void calculateTotalCoverTime(Map<String, List<Map<String, Object>>> result) {
//        for (Map.Entry<String, List<Map<String, Object>>> entry : result.entrySet()) {
//            String taskId = entry.getKey();
//            List<Map<String, Object>> equipments = entry.getValue();
//            if (equipments.size() == 0) continue;
//
//            Collections.sort(equipments, Comparator.comparing(o -> LocalDateTime.parse(o.get("entryTime").toString(), DateTimeFormatter.ISO_DATE_TIME)));
//
//            LocalDateTime currentStart = LocalDateTime.parse(equipments.get(0).get("entryTime").toString(), DateTimeFormatter.ISO_DATE_TIME);
//            LocalDateTime currentEnd = LocalDateTime.parse(equipments.get(0).get("exitTime").toString(), DateTimeFormatter.ISO_DATE_TIME);
//
//            Duration totalDuration = Duration.ZERO;
//
//            for (Map<String, Object> equipment : equipments) {
//                if (equipment.get("marked") != null && equipment.get("marked").equals(0)) continue;
//
//                LocalDateTime startTime = LocalDateTime.parse(equipment.get("entryTime").toString(), DateTimeFormatter.ISO_DATE_TIME);
//                LocalDateTime endTime = LocalDateTime.parse(equipment.get("exitTime").toString(), DateTimeFormatter.ISO_DATE_TIME);
//
//                if (startTime.isAfter(currentEnd)) {
//                    totalDuration = totalDuration.plus(Duration.between(currentStart, currentEnd));
//
//                    currentStart = startTime;
//                    currentEnd = endTime;
//                } else {
//                    currentEnd = currentEnd.isAfter(endTime) ? currentEnd : endTime;
//                }
//            }
//
//            totalDuration = totalDuration.plus(Duration.between(currentStart, currentEnd));
//
//            // 查询任务
//            RequirementTask task = requirementTaskService.getById(taskId);
//            long totalTime = Duration.between(task.getStartTime(), task.getEndTime()).getSeconds();
//            // 放第一个
//            double totalCover = (double) totalDuration.getSeconds() / totalTime;
//            equipments.get(0).put("totalCover", totalCover);
//            log.info("总覆盖率：{}秒", totalDuration.getSeconds());
//        }
//    }

//    private Map<String, List<Map<String, Object>>> resourceAllocation(Map<String, List<Map<String, Object>>> groupByTask) {
//        Map<String, List<Map<String, Object>>> newResult = new HashMap<>();
//        // 以任务ID重新分组
//        // 1、合并map中的value
//        List<Map<String, Object>> euipments = groupByTask.values().stream()
//                .flatMap(List::stream)
//                .collect(Collectors.toList());
//
//        // 2、开始分组
//        Map<String, List<Map<String, Object>>> groupByEquipment = euipments.stream()
//                .filter(e -> e.get("marked").equals(1))
//                .collect(Collectors.groupingBy(map -> map.get("id").toString()));
//
//        /**  3、判断每个设备的可测控目标数量是否大于任务数量，若小于目标数量则需要进行任务优先级判断 **/
////        List<String> allocTask = new ArrayList<>();
//        for (Map.Entry<String, List<Map<String, Object>>> entry : groupByEquipment.entrySet()) {
//            List<Map<String, Object>> tasks = entry.getValue();
//
//            // 需要清除已经分配的任务，避免重复分配
//            List<Map<String, Object>> filterTasks = tasks;
////            List<Map<String, Object>> filterTasks = tasks.stream()
////                    .filter(task -> !allocTask.contains(((RequirementTask) task.get("task")).getId()))
////                    .collect(Collectors.toList());
//
////            if (CollUtil.isEmpty(filterTasks)) continue;
//            // 这直接取第一个就行，因为集合中除了任务不一样，设备参数都是一样的
//            Map<String, Object> task = filterTasks.get(0);
//            int targetCount = Integer.parseInt(task.get("targetCount").toString());
//
//            // 满足测控目标数则跳出循环
//            if (targetCount >= filterTasks.size()) {
//                Map<String, List<Map<String, Object>>> entryGroup = filterTasks.stream()
//                        .collect(Collectors.groupingBy(m -> ((RequirementTask) m.get("task")).getId()));
//
////                allocTask.addAll(entryGroup.keySet());
//                mergeMap(newResult, entryGroup);
//                continue;
//            }
//
//            // 进行任务优先级排序，筛选出可测控目标数量的任务
//            List<Map<String, Object>> sortedTaskPriority = sortedTaskPriority(filterTasks, targetCount);
//            Map<String, List<Map<String, Object>>> sortedTaskPriorityMap = sortedTaskPriority.stream()
//                    .collect(Collectors.groupingBy(m -> ((RequirementTask) m.get("task")).getId()));
//
//            mergeMap(newResult, sortedTaskPriorityMap);
////            allocTask.addAll(sortedTaskPriorityMap.keySet());
//        }
//
//        return newResult;
//    }

//    private Map<String, List<Map<String, Object>>> resourceAllocation(Map<String, List<Map<String, Object>>> groupByTask) {
//        Map<String, List<Map<String, Object>>> newResult = new HashMap<>();
//
//        // 收集所有marked ！= 0 的数据，后面需要合并回结果中
//        List<Map<String, Object>> unmarkedData = groupByTask.values().stream()
//                .flatMap(List::stream)
//                .filter(e -> !e.get("marked").equals(1))
//                .collect(Collectors.toList());
//
//        // 1. 将所有任务实例合并成一个列表，并按任务ID分组
//        Map<String, List<Map<String, Object>>> groupedByTaskId = groupByTask.values().stream()
//                .flatMap(List::stream)
//                .filter(e -> e.get("marked").equals(1))  // 只处理已标记任务
//                .collect(Collectors.groupingBy(e -> ((RequirementTask) e.get("task")).getId()));
//
//        // 2. 初始化设备容量和已使用设备的记录
//        Map<String, Integer> equipmentCapacity = new HashMap<>();  // 设备容量
//        Map<String, Set<String>> taskUsedEquipment = new HashMap<>();  // 记录每个任务已使用的设备
//
//        // 3. 初始化设备容量
//        groupedByTaskId.values().stream()
//                .flatMap(List::stream)
//                .collect(Collectors.groupingBy(e -> e.get("id").toString()))  // 按设备ID分组
//                .forEach((equipmentId, equipmentTasks) -> {
//                    int targetCount = Integer.parseInt(equipmentTasks.get(0).get("targetCount").toString());
//                    equipmentCapacity.put(equipmentId, targetCount);
//                });
//
//        // 4. 遍历每个任务ID组
//        for (Map.Entry<String, List<Map<String, Object>>> taskEntry : groupedByTaskId.entrySet()) {
//            String taskId = taskEntry.getKey();
//            List<Map<String, Object>> taskInstances = taskEntry.getValue();
//
//            // 5. 按设备ID将任务实例归并（将同一设备的多个时间段视为一个整体）
//            Map<String, List<Map<String, Object>>> groupedByEquipmentId = taskInstances.stream()
//                    .collect(Collectors.groupingBy(e -> e.get("id").toString()));
//
//            List<Map<String, Object>> allocatedTasks = new ArrayList<>();
//
//            // 6. 遍历设备，并为当前任务分配
//            for (Map.Entry<String, List<Map<String, Object>>> equipmentEntry : groupedByEquipmentId.entrySet()) {
//                String equipmentId = equipmentEntry.getKey();
//                List<Map<String, Object>> equipmentTasks = equipmentEntry.getValue();
//
//                // 检查是否已经使用过该设备（一个任务只能用一次该设备）
//                taskUsedEquipment.putIfAbsent(taskId, new HashSet<>());
//                if (taskUsedEquipment.get(taskId).contains(equipmentId)) {
//                    continue;  // 该任务已经使用过此设备，跳过
//                }
//
//                // 检查设备的剩余容量是否足够
//                int remainingCapacity = equipmentCapacity.getOrDefault(equipmentId, 0);
//                if (remainingCapacity > 0) {
//                    // 分配一个任务实例，并减少设备容量
//                    allocatedTasks.addAll(equipmentTasks);  // 每次只取一个实例
//                    equipmentCapacity.put(equipmentId, remainingCapacity - 1);
//
//                    // 标记该设备已被当前任务使用
//                    taskUsedEquipment.get(taskId).add(equipmentId);
//                }
//
//                // 如果设备容量耗尽，停止分配
//                if (remainingCapacity <= 1) continue;
//            }
//
//            // 7. 将分配的任务添加到最终结果中
//            if (!allocatedTasks.isEmpty()) {
//                mergeResult(newResult, taskId, allocatedTasks);
//            }
//        }
//
//        // 合并结果集
//        unmarkedData.forEach(data -> {
//            String taskId = ((RequirementTask) data.get("task")).getId();
//            mergeResult(newResult, taskId, CollUtil.newArrayList(data));
//        });
//
//        return newResult;
//    }

//    private Map<String, List<Map<String, Object>>> resourceAllocation(Map<String, List<Map<String, Object>>> groupByTask) {
//        Map<String, List<Map<String, Object>>> newResult = new HashMap<>();
//
//        // 1. 收集所有 marked != 1 的数据，稍后合并回结果中
//        List<Map<String, Object>> unmarkedData = groupByTask.values().stream()
//                .flatMap(List::stream)
//                .filter(e -> !e.get("marked").equals(1))
//                .collect(Collectors.toList());
//
//        // 2. 按任务ID分组，只处理 marked == 1 的任务
//        Map<String, List<Map<String, Object>>> groupedByTaskId = groupByTask.values().stream()
//                .flatMap(List::stream)
//                .filter(e -> e.get("marked").equals(1))
//                .collect(Collectors.groupingBy(e -> ((RequirementTask) e.get("task")).getId()));
//
//        // 3. 初始化设备的可用容量和时间段使用记录
//        Map<String, Integer> equipmentCapacity = new HashMap<>();  // 设备的最大容量
//        Map<String, List<TimeSlot>> equipmentUsage = new HashMap<>();  // 记录设备的使用时间段及分配情况
//
//        // 4. 初始化设备的容量和时间段列表
//        groupedByTaskId.values().stream()
//                .flatMap(List::stream)
//                .collect(Collectors.groupingBy(e -> e.get("id").toString()))
//                .forEach((equipmentId, equipmentTasks) -> {
//                    int targetCount = Integer.parseInt(equipmentTasks.get(0).get("targetCount").toString());
//                    equipmentCapacity.put(equipmentId, targetCount);
//                    equipmentUsage.put(equipmentId, new ArrayList<>());
//                });
//
//        // 5. 遍历每个任务ID组
//        for (Map.Entry<String, List<Map<String, Object>>> taskEntry : groupedByTaskId.entrySet()) {
//            String taskId = taskEntry.getKey();
//            List<Map<String, Object>> taskInstances = taskEntry.getValue();
//
//            // 6. 按设备ID将任务实例分组
//            Map<String, List<Map<String, Object>>> groupedByEquipmentId = taskInstances.stream()
//                    .collect(Collectors.groupingBy(e -> e.get("id").toString()));
//
//            List<Map<String, Object>> allocatedTasks = new ArrayList<>();
//
//            // 7. 遍历设备，并为当前任务分配
//            for (Map.Entry<String, List<Map<String, Object>>> equipmentEntry : groupedByEquipmentId.entrySet()) {
//                String equipmentId = equipmentEntry.getKey();
//                List<Map<String, Object>> equipmentTasks = equipmentEntry.getValue();
//
//                // 遍历当前设备的任务实例，检查时间段是否能分配
//                for (Map<String, Object> equipmentTask : equipmentTasks) {
//                    LocalDateTime entryTime = LocalDateTime.parse(equipmentTask.get("entryTime").toString());
//                    LocalDateTime exitTime = LocalDateTime.parse(equipmentTask.get("exitTime").toString());
//
//                    if (canAllocate(equipmentUsage.get(equipmentId), entryTime, exitTime, equipmentCapacity.get(equipmentId))) {
//                        // 如果可以分配任务，则记录该任务实例，并更新时间段使用记录
//                        allocatedTasks.add(equipmentTask);
//                        equipmentUsage.get(equipmentId).add(new TimeSlot(entryTime, exitTime));
//    //                        break;  // 每次只分配一个实例
//                    }
//                }
//            }
//
//            // 8. 将已分配的任务添加到结果中
//            if (!allocatedTasks.isEmpty()) {
//                mergeResult(newResult, taskId, allocatedTasks);
//            }
//        }
//
//        // 9. 将未标记的数据合并回结果集
//        unmarkedData.forEach(data -> {
//            String taskId = ((RequirementTask) data.get("task")).getId();
//            mergeResult(newResult, taskId, CollUtil.toList(data));
//        });
//
//        return newResult;
//    }

    private Map<String, List<Map<String, Object>>> resourceAllocation(Map<String, List<Map<String, Object>>> groupByTask) {
        Map<String, List<Map<String, Object>>> newResult = new HashMap<>();

        // 1. 收集所有设备数据（包括已标记和备选设备）
        List<Map<String, Object>> allDevices = groupByTask.values().stream()
                .flatMap(List::stream)
                .filter(e -> e.get("marked").equals(1) || e.get("marked").equals(0))
                .collect(Collectors.toList());

        // 1.5. 从设备数据中提取原始时间信息（已在AbstractAlgorithmTemplate中保存）
        // 不需要额外保存，直接使用设备中的originalEntryTime和originalExitTime字段

        // 2. 收集所有其他 marked != 1 且 != 0 的数据，稍后合并回结果中
        List<Map<String, Object>> otherUnmarkedData = groupByTask.values().stream()
                .flatMap(List::stream)
                .filter(e -> !e.get("marked").equals(1) && !e.get("marked").equals(0))
                .collect(Collectors.toList());

        // 3. 按任务ID分组所有设备
        Map<String, List<Map<String, Object>>> groupedByTaskId = allDevices.stream()
                .collect(Collectors.groupingBy(e -> ((RequirementTask) e.get("task")).getId()));

        // 4. 初始化设备的可用容量和时间段使用记录
        Map<String, Integer> equipmentCapacity = new HashMap<>();  // 设备的最大容量
        Map<String, List<TimeSlot>> equipmentUsage = new HashMap<>();  // 记录设备的使用时间段及分配情况

        // 5. 初始化所有设备的容量和时间段列表
        allDevices.stream()
                .collect(Collectors.groupingBy(e -> e.get("id").toString()))
                .forEach((equipmentId, equipmentTasks) -> {
                    int targetCount = Integer.parseInt(equipmentTasks.get(0).get("targetCount").toString());
                    equipmentCapacity.put(equipmentId, targetCount);
                    equipmentUsage.put(equipmentId, new ArrayList<>());
                });

        // 6. 按任务优先级排序处理任务，新增约束条件：
        // - 优先分配generalId为6的任务（通过TaskTargetRelation表查询）
        // - 在相同优先级下，importance 数值越大优先级越高
        List<Map.Entry<String, List<Map<String, Object>>>> sortedTaskEntries = groupedByTaskId.entrySet().stream()
                .sorted((entry1, entry2) -> {
                    RequirementTask task1 = (RequirementTask) entry1.getValue().get(0).get("task");
                    RequirementTask task2 = (RequirementTask) entry2.getValue().get(0).get("task");

                    // 通过TaskTargetRelation表查询generalId，判断是否为优先任务（generalId=6）
                    boolean task1IsPriority = isTaskWithGeneralId(task1.getId(), "6");
                    boolean task2IsPriority = isTaskWithGeneralId(task2.getId(), "6");

                    if (task1IsPriority && !task2IsPriority) {
                        return -1; // task1优先
                    } else if (!task1IsPriority && task2IsPriority) {
                        return 1;  // task2优先
                    } else {
                        // 如果都是或都不是优先任务，则按importance排序
                        return Integer.compare(task2.getImportance(), task1.getImportance()); // 降序排列，优先级高的先处理
                    }
                })
                .collect(Collectors.toList());

        // 7. 遍历每个任务ID组（按优先级顺序）
        for (Map.Entry<String, List<Map<String, Object>>> taskEntry : sortedTaskEntries) {
            String taskId = taskEntry.getKey();
            List<Map<String, Object>> taskDevices = taskEntry.getValue();

            // 7.5. 在分配前恢复设备的原始时间信息
            restoreOriginalTimeInfoFromDevice(taskDevices);

            // 8. 使用覆盖率优先算法分配设备
            List<Map<String, Object>> allocatedTasks = allocateDevicesWithCoverageOptimization(
                    taskId, taskDevices, equipmentCapacity, equipmentUsage);

            // 8.5. 解决同一任务下设备时间重叠问题
            if (!allocatedTasks.isEmpty()) {
                resolveTimeOverlaps(allocatedTasks);
            }

            // 9. 将已分配的任务添加到结果中
            if (!allocatedTasks.isEmpty()) {
                mergeResult(newResult, taskId, allocatedTasks);
            }
        }

        // 10. 将未使用的设备和其他未标记的数据合并回结果集
        allDevices.forEach(data -> {
            // 只有未被选中的设备才合并回去（marked 仍为 0）
            if (data.get("marked").equals(0)) {
                String taskId = ((RequirementTask) data.get("task")).getId();
                mergeResult(newResult, taskId, CollUtil.toList(data));
            }
        });

        otherUnmarkedData.forEach(data -> {
            String taskId = ((RequirementTask) data.get("task")).getId();
            mergeResult(newResult, taskId, CollUtil.toList(data));
        });

        return newResult;
    }

    // 辅助方法：根据任务ID查询TaskTargetRelation表，判断是否为指定generalId的任务
    private boolean isTaskWithGeneralId(String taskId, String targetGeneralId) {
        try {
            RequirementTask task = requirementTaskService.getById(taskId);
            if (task != null && task.getTargetRelationId() != null) {
                TaskTargetRelation targetRelation = taskTargetRelationService.getById(task.getTargetRelationId());
                if (targetRelation != null && targetRelation.getGeneralId() != null) {
                    return targetGeneralId.equals(targetRelation.getGeneralId());
                }
            }
        } catch (Exception e) {
            log.warn("查询任务 {} 的generalId时发生异常: {}", taskId, e.getMessage());
        }
        return false;
    }

    // 辅助方法：使用覆盖率优化算法分配设备
    private List<Map<String, Object>> allocateDevicesWithCoverageOptimization(String taskId,
                                                                               List<Map<String, Object>> taskDevices,
                                                                               Map<String, Integer> equipmentCapacity,
                                                                               Map<String, List<TimeSlot>> equipmentUsage) {
        List<Map<String, Object>> allocatedTasks = new ArrayList<>();

        // 1. 先尝试单个设备达到100%覆盖，新增约束：对于generalId=6的任务，优先使用指定ID的设备
        List<Map<String, Object>> singleDeviceCandidates = taskDevices.stream()
                .filter(device -> {
                    double coverRate = Double.parseDouble(device.get("coverRate").toString());
                    return coverRate >= 1.0; // 100%覆盖
                })
                .sorted((d1, d2) -> {
                    // 获取任务信息，判断是否为generalId=6的任务
                    RequirementTask task = (RequirementTask) d1.get("task");
                    boolean isTaskWithGeneralId6 = isTaskWithGeneralId(task.getId(), "6");

                    // 获取设备ID
                    String deviceId1 = d1.get("id") != null ? d1.get("id").toString() : "";
                    String deviceId2 = d2.get("id") != null ? d2.get("id").toString() : "";

                    // 指定优先分配的设备ID（可以根据需要修改这个ID）
                    String priorityDeviceId = "your_priority_device_id_here"; // TODO: 替换为实际的设备ID

                    boolean isDevice1Priority = priorityDeviceId.equals(deviceId1);
                    boolean isDevice2Priority = priorityDeviceId.equals(deviceId2);

                    // 如果是generalId=6的任务，优先使用指定ID的设备
                    if (isTaskWithGeneralId6) {
                        if (isDevice1Priority && !isDevice2Priority) {
                            return -1; // d1优先
                        } else if (!isDevice1Priority && isDevice2Priority) {
                            return 1;  // d2优先
                        }
                    }

                    // 优先选择已标记的设备，然后按覆盖率排序
                    int marked1 = (Integer) d1.get("marked");
                    int marked2 = (Integer) d2.get("marked");
                    if (marked1 != marked2) {
                        return Integer.compare(marked2, marked1); // marked=1的优先
                    }
                    double rate1 = Double.parseDouble(d1.get("coverRate").toString());
                    double rate2 = Double.parseDouble(d2.get("coverRate").toString());
                    return Double.compare(rate2, rate1); // 覆盖率高的优先
                })
                .collect(Collectors.toList());

        // 2. 尝试分配单个100%覆盖的设备
        for (Map<String, Object> device : singleDeviceCandidates) {
            if (tryAllocateDevice(device, equipmentCapacity, equipmentUsage)) {
                allocatedTasks.add(device);
                // 将其他设备标记为0（不需要）
                markOtherDevicesAsUnused(taskDevices, device);
                return allocatedTasks;
            }
        }

        // 3. 如果没有单个设备能达到100%，尝试多设备组合
        return allocateMultipleDevicesForCoverage(taskId, taskDevices, equipmentCapacity, equipmentUsage);
    }

    // 辅助方法：尝试分配单个设备
    private boolean tryAllocateDevice(Map<String, Object> device,
                                      Map<String, Integer> equipmentCapacity,
                                      Map<String, List<TimeSlot>> equipmentUsage) {
        String equipmentId = device.get("id").toString();
        LocalDateTime entryTime = LocalDateTime.parse(device.get("entryTime").toString());
        LocalDateTime exitTime = LocalDateTime.parse(device.get("exitTime").toString());

        if (canAllocate(equipmentUsage.get(equipmentId), entryTime, exitTime, equipmentCapacity.get(equipmentId))) {
            // 分配成功，将 marked 修改为 1，并记录时间段使用
            device.put("marked", 1);
            equipmentUsage.get(equipmentId).add(new TimeSlot(entryTime, exitTime));
            return true;
        }
        return false;
    }

    // 辅助方法：将其他设备标记为不使用
    private void markOtherDevicesAsUnused(List<Map<String, Object>> taskDevices, Map<String, Object> selectedDevice) {
        for (Map<String, Object> device : taskDevices) {
            if (device != selectedDevice) {
                device.put("marked", 0);
            }
        }
    }

    // 辅助方法：多设备组合分配以达到最佳覆盖率
    private List<Map<String, Object>> allocateMultipleDevicesForCoverage(String taskId,
                                                                          List<Map<String, Object>> taskDevices,
                                                                          Map<String, Integer> equipmentCapacity,
                                                                          Map<String, List<TimeSlot>> equipmentUsage) {
        List<Map<String, Object>> allocatedTasks = new ArrayList<>();

        // 按覆盖率和优先级排序设备，新增约束：对于generalId=6的任务，优先使用指定ID的设备
        List<Map<String, Object>> sortedDevices = taskDevices.stream()
                .sorted((d1, d2) -> {
                    // 获取任务信息，判断是否为generalId=6的任务
                    RequirementTask task = (RequirementTask) d1.get("task");
                    boolean isTaskWithGeneralId6 = isTaskWithGeneralId(task.getId(), "6");

                    // 获取设备ID
                    String deviceId1 = d1.get("id") != null ? d1.get("id").toString() : "";
                    String deviceId2 = d2.get("id") != null ? d2.get("id").toString() : "";

                    // 指定优先分配的设备ID（可以根据需要修改这个ID）
                    String priorityDeviceId = "your_priority_device_id_here"; // TODO: 替换为实际的设备ID

                    boolean isDevice1Priority = priorityDeviceId.equals(deviceId1);
                    boolean isDevice2Priority = priorityDeviceId.equals(deviceId2);

                    // 如果是generalId=6的任务，优先使用指定ID的设备
                    if (isTaskWithGeneralId6) {
                        if (isDevice1Priority && !isDevice2Priority) {
                            return -1; // d1优先
                        } else if (!isDevice1Priority && isDevice2Priority) {
                            return 1;  // d2优先
                        }
                    }

                    // 优先选择已标记的设备
                    int marked1 = (Integer) d1.get("marked");
                    int marked2 = (Integer) d2.get("marked");
                    if (marked1 != marked2) {
                        return Integer.compare(marked2, marked1); // marked=1的优先
                    }
                    // 然后按覆盖率排序
                    double rate1 = Double.parseDouble(d1.get("coverRate").toString());
                    double rate2 = Double.parseDouble(d2.get("coverRate").toString());
                    return Double.compare(rate2, rate1); // 覆盖率高的优先
                })
                .collect(Collectors.toList());

        // 贪心算法：逐个添加设备直到达到100%覆盖或无法再添加
        double totalCoverage = 0.0;
        List<Polygon> allocatedPolygons = new ArrayList<>();

        for (Map<String, Object> device : sortedDevices) {
            if (tryAllocateDevice(device, equipmentCapacity, equipmentUsage)) {
                allocatedTasks.add(device);

                // 计算当前总覆盖率（需要考虑重叠）
                Polygon devicePolygon = (Polygon) device.get("polygon");
                if (devicePolygon != null) {
                    allocatedPolygons.add(devicePolygon);
                    totalCoverage = calculateCombinedCoverage(allocatedPolygons, taskId);

                    // 如果达到100%覆盖，停止添加更多设备
                    if (totalCoverage >= 1.0) {
                        break;
                    }
                }
            }
        }

        // 将未分配的设备标记为0
        for (Map<String, Object> device : taskDevices) {
            if (!allocatedTasks.contains(device)) {
                device.put("marked", 0);
            }
        }

        return allocatedTasks;
    }

    // 辅助方法：检查时间段是否能分配（是否有容量限制）
    private boolean canAllocate(List<TimeSlot> usedSlots, LocalDateTime entryTime, LocalDateTime exitTime, int capacity) {
        int concurrentCount = 0;
        for (TimeSlot slot : usedSlots) {
            if (slot.overlaps(entryTime, exitTime)) {
                concurrentCount++;
                if (concurrentCount >= capacity) {
                    return false;  // 超过容量限制，不能分配
                }
            }
        }
        return true;  // 未超过容量限制，可以分配
    }

    // 辅助方法：计算多个设备的组合覆盖率（考虑重叠）
    private double calculateCombinedCoverage(List<Polygon> polygons, String taskId) {
        if (polygons.isEmpty()) {
            return 0.0;
        }

        try {
            // 合并所有polygon区域，自动处理重叠
            Geometry combinePolygon = GeometryCombiner.combine(polygons).union();

            // 获取任务的航迹
            RequirementTask task = requirementTaskService.getById(taskId);
            Map<String, List<RequirementTargetTrack>> trackByTask = queryTargetTrackByTask(taskId);
            Map<String, LineString> trackMap = buildTrackMap(trackByTask);

            LineString lineString = trackMap.get(task.getTargetRelationId());
            if (lineString != null && combinePolygon != null) {
                Geometry intersection = lineString.intersection(combinePolygon);
                return intersection.getLength() / lineString.getLength();
            }
        } catch (Exception e) {
            log.warn("计算组合覆盖率时出现异常: {}", e.getMessage());
        }

        return 0.0;
    }

    // 辅助方法：从设备数据中恢复原始时间信息
    private void restoreOriginalTimeInfoFromDevice(List<Map<String, Object>> taskDevices) {
        for (Map<String, Object> device : taskDevices) {
            // 检查设备是否有原始时间信息
            if (device.get("originalEntryTime") != null && device.get("originalExitTime") != null) {
                String originalEntryTime = device.get("originalEntryTime").toString();
                String originalExitTime = device.get("originalExitTime").toString();

                // 恢复原始时间
                device.put("entryTime", parseDateTime(originalEntryTime));
                device.put("exitTime", parseDateTime(originalExitTime));

                log.debug("恢复设备 {} 的原始时间: {} - {}",
                        device.get("id"), originalEntryTime, originalExitTime);
            } else {
                log.debug("设备 {} 没有原始时间信息，使用当前时间", device.get("id"));
            }
        }
    }

    // 辅助方法：解决同一任务下设备时间重叠问题
    private void resolveTimeOverlaps(List<Map<String, Object>> allocatedTasks) {
        if (allocatedTasks.size() <= 1) {
            return; // 只有一个或没有设备，无需处理重叠
        }

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 按entryTime排序，确保按时间顺序处理
        allocatedTasks.sort((d1, d2) -> {
            LocalDateTime time1 = parseDateTime(d1.get("entryTime").toString());
            LocalDateTime time2 = parseDateTime(d2.get("entryTime").toString());
            return time1.compareTo(time2);
        });

        // 遍历设备，检查并解决时间重叠
        for (int i = 1; i < allocatedTasks.size(); i++) {
            Map<String, Object> currentDevice = allocatedTasks.get(i);
            LocalDateTime currentEntryTime = parseDateTime(currentDevice.get("entryTime").toString());
            LocalDateTime currentExitTime = parseDateTime(currentDevice.get("exitTime").toString());

            // 检查与前面所有设备的时间重叠
            for (int j = 0; j < i; j++) {
                Map<String, Object> previousDevice = allocatedTasks.get(j);
                LocalDateTime previousEntryTime = parseDateTime(previousDevice.get("entryTime").toString());
                LocalDateTime previousExitTime = parseDateTime(previousDevice.get("exitTime").toString());

                // 检查是否有时间重叠：当前设备开始时间在前一个设备结束时间之前
                if (currentEntryTime.isBefore(previousExitTime)) {
                    // 调整当前设备的开始时间为前一个设备的结束时间
                    currentDevice.put("entryTime", previousExitTime.format(formatter));
                    currentEntryTime = previousExitTime;

                    // 如果调整后开始时间晚于或等于结束时间，则将该设备标记为不可用
                    if (!currentEntryTime.isBefore(currentExitTime)) {
                        currentDevice.put("marked", 0);
                        log.warn("设备 {} 在调整时间重叠后时间段无效，已标记为不可用",
                                currentDevice.get("id"));
                        break;
                    }
                }
            }
        }

        // 移除被标记为不可用的设备
        allocatedTasks.removeIf(device -> device.get("marked").equals(0));
    }

    // 辅助方法：解析时间字符串，支持多种格式
    private LocalDateTime parseDateTime(String timeStr) {
        if (timeStr == null || timeStr.trim().isEmpty()) {
            return null;
        }

        try {
            // 尝试解析 ISO 格式 (2025-09-01T10:47:16.835)
            return LocalDateTime.parse(timeStr);
        } catch (Exception e1) {
            try {
                // 尝试解析标准格式 (yyyy-MM-dd HH:mm:ss)
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                return LocalDateTime.parse(timeStr, formatter);
            } catch (Exception e2) {
                log.warn("无法解析时间字符串: {}", timeStr);
                throw new RuntimeException("时间格式解析失败: " + timeStr);
            }
        }
    }

    // 辅助方法：合并结果到最终Map
    private void mergeResult(Map<String, List<Map<String, Object>>> result,
                             String taskId, List<Map<String, Object>> toMerge) {
        result.merge(taskId, toMerge, (existing, newItems) -> {
            existing.addAll(newItems);
            return existing;
        });
    }

    // 辅助方法：任务优先级排序
    private List<Map<String, Object>> sortedTaskPriority(List<Map<String, Object>> tasks, int limit) {
        return tasks.stream()
                .sorted(Comparator.comparingInt(t ->
                        ((RequirementTask) t.get("task")).getImportance())) // 按优先级排序
                .limit(limit) // 限制数量
                .collect(Collectors.toList());
    }

    private Map<String, List<Map<String, Object>>> mergeMap(Map<String, List<Map<String, Object>>> map1,
                                                            Map<String, List<Map<String, Object>>> map2) {
        // 将map2添加到map1中
        map2.forEach((k, v) -> {
            map1.merge(k, v, (existingList, newList) -> {
                existingList.addAll(newList);
                return existingList;
            });
        });
        return map1;
    }

    // 任务优先级排序
//    private List<Map<String, Object>> sortedTaskPriority(List<Map<String, Object>> tasks, int targetCount) {
//        List<Map<String, Object>> result = new ArrayList<>(tasks.size());
//        // 先使用重要程度进行排序
//        tasks.sort(Comparator.comparing(map -> ((RequirementTask) map.get("task")).getImportance()));
//
//        // 使用重要程度进行分组
//        Map<Integer, List<Map<String, Object>>> groupByImportance = tasks.stream()
//                .collect(Collectors.groupingBy(map -> ((RequirementTask) map.get("task")).getImportance()));
//
//        List<Integer> importances = groupByImportance.keySet().stream().sorted(Comparator.reverseOrder()).collect(Collectors.toList());
//
//        int count = targetCount;
//        for (Integer importance : importances) {
//            if (count <= 0) break;
//            List<Map<String, Object>> taskList = groupByImportance.get(importance);
//
//            // 如果大于则直接取集合中count数量的任务进行返回
//            if (taskList.size() >= targetCount) {
//                result.addAll(taskList.subList(0, count));
//            } else {
//                // 若小于，则先把这个集合数量分配了
//                result.addAll(taskList);
//            }
//            count = targetCount - taskList.size();
//        }
//
//        return result;
//    }

    private void insertEquipmentOccupancy(ConfirmScheduleDTO confirmScheduleDTO) {
        List<ConfirmScheduleDTO.EquipmentDTO> equipments = confirmScheduleDTO.getEquipments();

        List<DataEquipmentOccupancy> equipmentOccupancies = equipments.stream()
                .filter(e -> e.getStartTime() != null || e.getEndTime() != null)
                .map(equipment -> {
                    DataEquipmentOccupancy occupancy = equipment.convert();
                    occupancy.setTaskId(confirmScheduleDTO.getTaskId());
                    return occupancy;
                })
                .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(equipmentOccupancies)) {
            dataEquipmentOccupancyService.saveOrUpdateBatch(equipmentOccupancies);
        }
    }

    private void checkEquipmentUsageTime(ConfirmScheduleDTO confirmScheduleDTO) {
        List<ConfirmScheduleDTO.EquipmentDTO> equipments = confirmScheduleDTO.getEquipments();

        for (ConfirmScheduleDTO.EquipmentDTO equipment : equipments) {
            // 如果查询有数据则表示该时间段已被占用
            int count = dataEquipmentOccupancyService.count(Wrappers.<DataEquipmentOccupancy>lambdaQuery()
                    .eq(DataEquipmentOccupancy::getGeneralId, equipment.getGeneralId())
                    .eq(DataEquipmentOccupancy::getEquipmentId, equipment.getEquipmentId())
                    .eq(DataEquipmentOccupancy::getAreaId, equipment.getAreaId())
                    .le(DataEquipmentOccupancy::getEndTime, equipment.getStartTime())
                    .ge(DataEquipmentOccupancy::getStartTime, equipment.getEndTime()));

            if (count > 0) {
                log.error("资源设备{}， 在时间段{}-{}已被占用，请重新选择资源设备", equipment.getEquipmentId(), equipment.getStartTime(), equipment.getEndTime());
                throw new ServiceException("资源设备已被占用，请重新选择资源设备");
            }
        }
    }


    public void buildPolygon(Map<String, List<Map<String, Object>>> equipments) {
        for (Map.Entry<String, List<Map<String, Object>>> entry : equipments.entrySet()) {
            List<Map<String, Object>> eps = entry.getValue();
            // 需要根据不同的设备类型来进行区域判断
            EquipmentEnum type = EquipmentEnum.getEnumByCode(Integer.parseInt(entry.getKey()));
            switch (type) {
                case LAND_BASED:
                case SEA_BASED:
                case VACANT_PART:
                    // 获取底面区域，只判断二维距离即可
                    eps.forEach(map -> {
                        double longitude = Double.parseDouble(map.get("longitude").toString());
                        double latitude = Double.parseDouble(map.get("latitude").toString());
                        double altitude = Double.parseDouble(map.get("altitude").toString());
                        double radius = Double.parseDouble(map.get("radius").toString());

//                        Coordinate center = new Coordinate(longitude, latitude, altitude);
//                        Polygon polygon = GISUtils.createCircle(center, radius, geometryFactory);

                        // 创建区域
                        Polygon polygon = createArea(latitude, longitude, radius, geometryFactory);

                        // 由于是动态查询,所有这里必须要将具体的参数返回,不然后面无法查询
                        map.put("polygon", polygon);
                    });
                    break;
                case SPACE_BASED:
                    eps.forEach(map -> {
                        double longitude = Double.parseDouble(map.get("longitude").toString());
                        double latitude = Double.parseDouble(map.get("latitude").toString());
                        double altitude = Double.parseDouble(map.get("altitude").toString());
                        double radius = Double.parseDouble(map.get("bottomRadius").toString());

                        // 创建区域
                        Polygon polygon = createArea(latitude, longitude, radius, geometryFactory);

                        map.put("polygon", polygon);
                    });
                    break;
//                case VACANT_PART:
//                    break;
                default:
                    break;
            }
        }

    }

    private Polygon createArea(double latitude, double longitude, double radius, GeometryFactory geometryFactory) {
        Polygon polygon;
        try {
            polygon = GISUtils.createCircle0(latitude, longitude, radius, geometryFactory);
        } catch (Exception e) {
            log.error("构建地图区域失败", e);
            throw new ServiceException("构建地图区域失败");
        }

        return polygon;
    }

    @Override
    public Map<String, LineString> buildTrackMap(Map<String, List<RequirementTargetTrack>> tracks) {
        Map<String, LineString> result = new HashMap<>();
        for (Map.Entry<String, List<RequirementTargetTrack>> entry : tracks.entrySet()) {
            List<RequirementTargetTrack> trackList = entry.getValue();

            // 转换
            Coordinate[] coordinates = trackList.stream()
                    .map(track -> new Coordinate(track.getLongitude().doubleValue(), track.getLatitude().doubleValue(), track.getAltitude().doubleValue()))
                    .toArray(Coordinate[]::new);

            // 创建
            LineString line = geometryFactory.createLineString(coordinates);

            result.put(entry.getKey(), line);
        }

        return result;
    }

    @Override
    public Map<String, LineString> buildTrackMap0(Map<String, CoordinateWithTimeDTO[]> coordinateMaps) {
        Map<String, LineString> result = new HashMap<>();
        for (Map.Entry<String, CoordinateWithTimeDTO[]> entry : coordinateMaps.entrySet()) {
            CoordinateWithTimeDTO[] coordinateWithTimeDTOS = entry.getValue();

            List<CoordinateWithTimeDTO> trackList = Arrays.asList(coordinateWithTimeDTOS);
            // 转换
            Coordinate[] coordinates = trackList.stream()
                    .map(track -> new Coordinate(track.getCoordinate().getX(), track.getCoordinate().getY(), track.getCoordinate().getZ()))
                    .toArray(Coordinate[]::new);

            // 创建
            LineString line = geometryFactory.createLineString(coordinates);

            result.put(entry.getKey(), line);
        }

        return result;
    }

//    @Override
//    public List<LineString> mergeLineStrings(List<LineString> lineStrings) {
//        List<LineString> result = new ArrayList<>(lineStrings.size());
//        for (int i = 0; i < lineStrings.size(); i++) {
//            if (i != lineStrings.size() - 1) {
//                LineString ls1 = lineStrings.get(i);
//                LineString ls2 = lineStrings.get(i + 1);
//
//                Coordinate[] coords1 = ls1.getCoordinates();
//                Coordinate[] coords2 = ls2.getCoordinates();
//
//                // 检查是否共线且有公共端点
//                int n = coords1.length;
//                if (n > 2 && areCollinear(coords1[n - 2], coords1[n - 1], coords2[1])) {
//                    Coordinate[] mergedCoords = new Coordinate[]{coords1[n - 2], coords2[1]};
//                    LineString mergeLine = geometryFactory.createLineString(mergedCoords);
//
//                    result.add(mergeLine);
//                } else if (areCollinear(coords1[0], coords1[1], coords2[1])) {
//                    Coordinate[] mergedCoords = new Coordinate[]{coords1[0], coords2[1]};
//                    LineString mergeLine = geometryFactory.createLineString(mergedCoords);
//
//                    result.add(mergeLine);
//                } else {
//                    // 如果没有公共端点则添加该线段
//                    result.add(ls1);
//                }
//            }
//        }
//
//        return result;
//    }

    @Override
    public List<LineString> mergeLineStrings(List<LineString> lineStrings) {
        List<LineString> result = new ArrayList<>();
        LineString currentLine = lineStrings.get(0); // 初始化为第一条线段

        for (int i = 1; i < lineStrings.size(); i++) {
            LineString nextLine = lineStrings.get(i);

            // 尝试合并当前线段和下一条线段
            LineString mergedLine = tryMerge(currentLine, nextLine);

            if (mergedLine != null) {
                // 如果合并成功，则更新当前线段为合并后的结果
                currentLine = mergedLine;
            } else {
                // 如果不能合并，则保存当前线段，并将下一条线段作为新的 currentLine
                result.add(currentLine);
                currentLine = nextLine;
            }
        }

        // 最后一个合并结果需要添加到结果列表中
        result.add(currentLine);

        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void handlerResponseScheduleResult(List<RespScheduleDTO> resp) {
        for (RespScheduleDTO respScheduleDTO : resp) {
            // 修改设备占用
            DataEquipmentOccupancy occupancy = dataEquipmentOccupancyService.getOne(Wrappers.<DataEquipmentOccupancy>lambdaQuery()
                    .eq(DataEquipmentOccupancy::getTaskId, respScheduleDTO.getTaskId())
                    .eq(DataEquipmentOccupancy::getEquipmentId, respScheduleDTO.getEquipmentId()));

            if (occupancy != null) {
                occupancy.setScheduleState((int) respScheduleDTO.getState());

                dataEquipmentOccupancyService.updateById(occupancy);
                log.info("修改设备调度状态成功，任务ID：{}, 状态：{}", respScheduleDTO.getTaskId(),
                        respScheduleDTO.getState());
            } else {
                // 如果为空则参数有问题，需排查参数是否正确
                log.error("修改设备调度状态失败，任务ID：{}， 节点ID：{}", respScheduleDTO.getTaskId(),
                        respScheduleDTO.getEquipmentId());
            }
        }
    }

    private LineString tryMerge(LineString ls1, LineString ls2) {
        Coordinate[] coords1 = ls1.getCoordinates();
        Coordinate[] coords2 = ls2.getCoordinates();

        int n = coords1.length;
        GeometryFactory geometryFactory = new GeometryFactory();

        // 检查是否共线且有公共端点
        if (n > 2 && areFourPointsCollinear(coords1[n - 2], coords1[n - 1], coords2[0], coords2[1])) {
            Coordinate[] mergedCoords = new Coordinate[]{coords1[n - 3], coords2[1]};
            return geometryFactory.createLineString(mergedCoords);
        } else if (areFourPointsCollinear(coords1[0], coords1[1], coords2[0], coords2[1])) {
            Coordinate[] mergedCoords = new Coordinate[]{coords1[0], coords2[1]};
            return geometryFactory.createLineString(mergedCoords);
        }
        // 如果不能合并则返回 null
        return null;
    }


    // 判断三点是否共线：叉积为0表示共线
    private static boolean areCollinear(Coordinate a, Coordinate b, Coordinate c) {
        double crossProduct = (b.y - a.y) * (c.x - b.x) - (b.x - a.x) * (c.y - b.y);
        return Math.abs(crossProduct) < 1e-10;  // 允许一定的浮点误差
    }

    // 判断四点是否共线：使用叉积，允许一定的浮点误差
    private static boolean areFourPointsCollinear(Coordinate a, Coordinate b, Coordinate c, Coordinate d) {
        // 计算 (AB x BC) 和 (BC x CD) 的叉积
        double cross1 = crossProduct(a, b, c);
        double cross2 = crossProduct(b, c, d);

        // 叉积接近0表示共线（允许浮点误差）
        return Math.abs(cross1) < 1e-10 && Math.abs(cross2) < 1e-10;
    }

    // 计算三点的叉积
    private static double crossProduct(Coordinate a, Coordinate b, Coordinate c) {
        return (b.y - a.y) * (c.x - b.x) - (b.x - a.x) * (c.y - b.y);
    }

    public Map<String, CoordinateWithTimeDTO[]> buildCoordinateMaps(Map<String, List<RequirementTargetTrack>> tracks, RequirementTask task) {
        Map<String, CoordinateWithTimeDTO[]> coordinateMaps = new HashMap<>();
        for (Map.Entry<String, List<RequirementTargetTrack>> entry : tracks.entrySet()) {
            List<RequirementTargetTrack> trackList = entry.getValue();
//            String relationId = entry.getKey();
//            TaskTargetRelation relation = taskTargetRelationService.getById(relationId);

            CoordinateWithTimeDTO[] coordinates0 = convertCoordinate(trackList, task);

            coordinateMaps.put(entry.getKey(), coordinates0);
        }

        return coordinateMaps;
    }



//    private CoordinateWithTimeDTO[] convertCoordinate(List<RequirementTargetTrack> trackList, RequirementTask task) {
//        return trackList.stream()
//                .map(track -> {
//                    CoordinateWithTimeDTO dto = new CoordinateWithTimeDTO();
//                    Coordinate coordinate = new Coordinate(track.getLongitude().doubleValue(), track.getLatitude().doubleValue(), track.getAltitude().doubleValue());
//                    dto.setCoordinate(coordinate);
//
//                    // 以任务开始时间为基准时间
//
//                    if (task != null) {
//                        dto.setTime(DateUtil.toInstant(task.getStartTime().plusSeconds(track.getTime())));
//                    }
//
//                    return dto;
//                })
//                .toArray(CoordinateWithTimeDTO[]::new);
//    }

    private CoordinateWithTimeDTO[] convertCoordinate(List<RequirementTargetTrack> trackList, RequirementTask task) {
        TaskTargetRelation targetRelation = taskTargetRelationService.getById(task.getTargetRelationId());

        List<RequirementTargetTrackDTO> trackDTOS = trackList.stream()
                .map(RequirementTargetTrack::convert).collect(Collectors.toList());
        // 2024.08.13 UPDATE 计算任务覆盖航迹的经纬度关键点
        LinkedList<double[]> points = taskTargetService.calculateTaskPoint(task, trackDTOS, targetRelation);

        return points.stream()
                .map(track -> {
                    CoordinateWithTimeDTO dto = new CoordinateWithTimeDTO();
                    Coordinate coordinate = new Coordinate(track[1], track[0], 0);
                    dto.setCoordinate(coordinate);

                    // 以航迹开始时间为基准时间
                    if (task != null) {
                        dto.setTime(DateUtil.toInstant(targetRelation.getStartTime().plusSeconds((int) track[2])));
                    }

                    return dto;
                })
                .toArray(CoordinateWithTimeDTO[]::new);
    }

    @Override
    public Object calculateCover(CalculateCoverDTO calculateCoverDTO) {
        double coverageRate = 0.0;
//        DataGeneral general = dataGeneralService.getById(calculateCoverDTO.getGeneralId());
        List<Map<String, Object>> equipments = calculateCoverDTO.getEquipments();

        List<Polygon> polygons = new ArrayList<>();
        List<String> taskIds = new ArrayList<>();
        for (Map<String, Object> equipment : equipments) {
            Map<String, List<Map<String, Object>>> equipmentParams = new HashMap<>();
            equipmentParams.put(equipment.get("dataType").toString(), Collections.singletonList(equipment));

            // 构建区域
            buildPolygon(equipmentParams);

            polygons.add((Polygon) equipment.get("polygon"));

            taskIds.add(equipment.get("taskId").toString());

            String entryTime = equipment.get("entryTime").toString();
            String exitTime = equipment.get("exitTime").toString();
            LocalDateTime entryDate = cn.hutool.core.date.DateUtil.parseLocalDateTime(entryTime, "yyyy-MM-dd HH:mm:ss");
            LocalDateTime exitDate = cn.hutool.core.date.DateUtil.parseLocalDateTime(exitTime, "yyyy-MM-dd HH:mm:ss");

            String isoDate = entryDate.atZone(ZoneId.of("UTC")).format(DateTimeFormatter.ISO_DATE_TIME);
            String exitIso = exitDate.atZone(ZoneId.of("UTC")).format(DateTimeFormatter.ISO_DATE_TIME);
            equipment.put("entryTime", isoDate);
            equipment.put("exitTime", exitIso);
        }

        // 汇总区域
        Geometry combinePolygon = GeometryCombiner.combine(polygons).union();

        // 获取航迹
        Map<String, List<RequirementTargetTrack>> trackByTask = queryTargetTrackByTask(equipments.get(0).get("taskId").toString());

        // 构建线
        Map<String, LineString> track = buildTrackMap(trackByTask);

        for (LineString lineString : track.values()) {
            if (combinePolygon != null) {
                Geometry intersection = lineString.intersection(combinePolygon);

                coverageRate = intersection.getLength() / lineString.getLength();
            }

            log.info("覆盖率计算结果为：{}", coverageRate);
        }

        // 封装调用调度算法接口
        AlgorithmDTO algorithmDTO = new AlgorithmDTO();
        algorithmDTO.setTaskIds(taskIds);
        algorithmDTO.setAlgo(AlgorithmContextEnum.SORTED_COVER.getCode());
        algorithmDTO.setEquipments(equipments);

        Map<String, List<Map<String, Object>>> equipment = invokeAlgorithm(algorithmDTO, new Page());

        return equipment;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void startSchedule() {
        // 筛选出需求进度调度的任务
        List<RequirementTaskDTO> tasks = queryWaitingScheduleTask();

        if (CollUtil.isEmpty(tasks)) {
            log.info("当前没有待调度的任务");
            return;
        }

        // 将任务状态变更为调度中
        changeTaskStatus(tasks, TaskScheduleStatusEnum.SCHEDULING);

        // 调用调度算法开始调度返回
        Map<String, List<Map<String, Object>>> scheduledTask = doSchedule(tasks);

        // 存储调度结果
        saveScheduleResult(scheduledTask);

        // 将任务状态变更为已调度
        changeTaskStatus(tasks, TaskScheduleStatusEnum.SCHEDULED_SUCCESS);
    }

    @Override
    public RequirementTask queryTaskByRelationId(String relationId) {
        TaskTargetRelation relation = taskTargetRelationService.getById(relationId);

        return requirementTaskService.getById(relation.getTaskId());
    }

    @Override
    public Polygon getSatellitePolygon(String targetId, Map<String, Object> equip, RequirementTask task) {
        // 计算当前时间位置
        LLAPredict coordinate = getSatelliteCoordinate(equip.get("id").toString(), equip.get("generalId").toString(), task);
        if (coordinate == null) return null;

        // 计算投影面积
        double radius = GISUtils.calculateProjectionRadius(coordinate.getAlt(), ((BigDecimal) equip.get("bearingAngle")).doubleValue());

        // 将经纬度转换为笛卡尔坐标
        double[] cartesian = GISUtils.latLonToCartesian(coordinate.getDegLat(), coordinate.getDegLon());

        Coordinate center = new Coordinate(cartesian[0], cartesian[1]);

        return GISUtils.createCircle(center, radius, geometryFactory);
//        return null;
    }

    @Override
    public double sendResult(ScheduleTaskDTO scheduleTaskDTO) {
        Random random = new Random();
        int currentStep = 0;

        List<Map<String, Object>> equipments = scheduleTaskDTO.getEquipments();
        BigDecimal resultProgress = BigDecimal.ZERO;
        for (int i = 0; i < equipments.size(); i++) {
            currentStep++;
            Map<String, Object> equipment = equipments.get(i);

            // 构建百分比
//            double progress = (double) currentStep / equipments.size();
//            progress = Double.parseDouble(String.format("%.2f", progress * 100));

            BigDecimal progress = BigDecimal.valueOf(currentStep).divide(BigDecimal.valueOf(scheduleTaskDTO.getTotal()), 6, RoundingMode.HALF_UP)
            .multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP);
            progress = BigDecimal.valueOf(scheduleTaskDTO.getProgress()).add(progress);

            // 容错判断
            if (progress.compareTo(BigDecimal.valueOf(100)) > 0 || progress.doubleValue() >= 99.9) {
                progress = BigDecimal.valueOf(100);
            }

            if (currentStep == equipments.size()) {
                resultProgress = progress;
            }

            int schedule;
            if (scheduleTaskDTO.getIsRandom() == 1) {
                // 构建协商信息,调度结果随机
                schedule = random.nextInt(2);
            } else {
                // 如果不是随机则为用户选择，用户选择默认都是可用，所以都为1
                schedule = equipment.get("schedule") != null ? Integer.parseInt(equipment.get("schedule").toString()) : 1;
            }

            // 这里是二级调度先全部返回失败
            if (scheduleTaskDTO.getType() == 2) schedule = 0;

            // 构建信息
            String message = buildMessage(equipment, schedule);

            // 构建最终结果
            ScheduleResultDTO.ScheduleResult result = buildResult(equipment, schedule);

            ScheduleResultDTO resultDTO = buildScheduleResultDTO(result, progress.doubleValue(), message);

            log.info("一级调度信息构建完成：{}", resultDTO);

            // 发送websocket给前端
            sendResult2Front(scheduleTaskDTO.getClientId(), resultDTO);

            // 保存调度信息
            saveScheduleLog(result, message, 1);
        }

        return resultProgress.doubleValue();
    }

    private void saveScheduleLog(ScheduleResultDTO.ScheduleResult result, String message, Integer type) {
        ScheduleLogDTO logDTO = new ScheduleLogDTO();
        logDTO.setTaskId(result.getTaskId());
        logDTO.setEquipmentId(result.getEquipmentId());
        logDTO.setTaskName(result.getTaskName());
        logDTO.setRecord(message);
        logDTO.setStatus(result.getResult());
        logDTO.setType(type);

        scheduleLogService.saveScheduleLog(logDTO);
    }

    private ScheduleResultDTO buildScheduleResultDTO(ScheduleResultDTO.ScheduleResult result, double progress, String message) {
        ScheduleResultDTO resultDTO = new ScheduleResultDTO();

        resultDTO.setResult(result);
        resultDTO.setProgress(progress);
        resultDTO.setMessage(message);
        return resultDTO;
    }

    @Override
    public void renegotiate(ScheduleTaskDTO scheduleTaskDTO) {
        List<DataArea> allArea = dataAreaService.list(Wrappers.<DataArea>lambdaQuery().eq(DataArea::getAreaType, 1));
//        Random random = new Random();
        List<Map<String, Object>> areas = scheduleTaskDTO.getAreas();
        int totalSteps = areas.size() * allArea.size();
        int currentStep = 0;

        // 这里area就是实际能覆盖到任务的域
        for (int i = 0; i < areas.size(); i++) {
            Map<String, Object> area = areas.get(i);
            String taskId = (String) area.get("taskId");
            String areaId = (String) area.get("areaId");
            Boolean allFailed = area.get("allFailed") != null ? (Boolean) area.get("allFailed") : false;

            RequirementTask task = requirementTaskService.getById(taskId);
//            List<Map<String, Object>> equipments = (List<Map<String, Object>>) area.get("equipments");

            // 构建百分比
            for (DataArea dataArea : allArea) {
                currentStep++;
                double progress = (double) currentStep / totalSteps;

                progress = Double.parseDouble(String.format("%.2f", progress * 100));

//                int success = random.nextInt(2);
                boolean success = false;
                if (!allFailed && dataArea.getId().equals(areaId)) {
                    // 如果资源域ID和传入的相同则表示该资源域下有能覆盖的资源
                    success = areaId.equals(dataArea.getId());
                }
                // 每个资源域随机派资源 0 表示没有资源可以调度，1表示有资源可以调度
                Map<String, Object> result = new HashMap<>();
                result.put("areaName", dataArea.getAreaName());
                StringBuilder sb = new StringBuilder();
                sb.append("资源域【")
                        .append(dataArea.getAreaName())
                        .append("】对于任务【")
                        .append(task.getTaskName())
                        .append("】");
//                if (success) {
                // 如果有资源，随机去该资源域下选择资源
//                    List<DataGeneral> generals = dataGeneralService.list(Wrappers.<DataGeneral>lambdaQuery()
//                            .in(DataGeneral::getDataType, Arrays.asList(1, 2, 3)));
//
//                    for (DataGeneral general : generals) {
//                        Map<String, Object> args = new HashMap<>();
//                        args.put("area_id", dataArea.getId());
//                        IPage<Map<String, Object>> page = commonMapper.getData(new Page<>(1, 10), general.getTableName(), null, null, args);
//                        if (CollUtil.isEmpty(page.getRecords())) continue;
//
//                        List<Map<String, Object>> records = page.getRecords();
//                        result = records.get(0);
//
//                        sb.append(" 【")
//                                .append(result.get("name").toString())
//                                .append("】可执行当前任务");
//
//                        break;
//                    }
//                }
                if (success) {
                    sb.append("可执行当前任务");
                } else {
                    sb.append("没有资源可以调度");
                }
                BeanUtil.beanToMap(area, result, false, false);

                result.put("task", task);
                result.put("taskId", task.getId());
                result.put("areaId", dataArea.getId());

                ScheduleResultDTO.ScheduleResult scheduleResult = buildResult(result, success ? 1 : 0);

                ScheduleResultDTO scheduleResultDTO = buildScheduleResultDTO(scheduleResult, progress, sb.toString());

                // 发送websocket给前端
                sendResult2Front(scheduleTaskDTO.getClientId(), scheduleResultDTO);

                // 保存调度信息
                saveScheduleLog(scheduleResult, sb.toString(), 2);
            }
        }
    }

//    @Override
//    public void renegotiate(ScheduleTaskDTO scheduleTaskDTO) {
//        // 兼容旧接口，转换为新的参数结构
//        RenegotiateDTO renegotiateDTO = convertToRenegotiateDTO(scheduleTaskDTO);
//        renegotiateWithCapacityManagement(renegotiateDTO);
//    }

    /**
     * 带容量管理的重新协商方法
     */
    public void renegotiateWithCapacityManagement(RenegotiateDTO renegotiateDTO) {
        log.info("开始重新协商，失败设备数量: {}, 任务数量: {}",
                renegotiateDTO.getFailedEquipments() != null ? renegotiateDTO.getFailedEquipments().size() : 0,
                renegotiateDTO.getTaskIds() != null ? renegotiateDTO.getTaskIds().size() : 0);

        // 1. 初始化设备容量管理
        Map<String, Integer> equipmentCapacity = new HashMap<>();
        Map<String, List<TimeSlot>> equipmentUsage = new HashMap<>();

        // 2. 构建已成功分配设备的容量占用情况
        buildCapacityFromSuccessEquipments(renegotiateDTO.getSuccessEquipments(), equipmentCapacity, equipmentUsage);

        // 3. 释放失败设备的容量
        releaseFailedEquipmentCapacity(renegotiateDTO.getFailedEquipments(), equipmentUsage);

        // 4. 按任务优先级重新分配
        List<String> taskIds = renegotiateDTO.getTaskIds();
        if (CollUtil.isEmpty(taskIds)) {
            log.warn("没有需要重新分配的任务");
            return;
        }

        // 获取任务信息并按优先级排序
        List<RequirementTask> tasks = new ArrayList<>(requirementTaskService.listByIds(taskIds));
        tasks.sort((t1, t2) -> Integer.compare(t2.getImportance(), t1.getImportance())); // 优先级高的先处理

        int totalSteps = tasks.size();
        int currentStep = 0;

        // 5. 逐个任务重新分配
        for (RequirementTask task : tasks) {
            currentStep++;
            double progress = (double) currentStep / totalSteps * 100;

            try {
                // 获取该任务的备选设备
                List<Map<String, Object>> backupDevices = renegotiateDTO.getBackupEquipmentsGroup().get(task.getId());
                if (CollUtil.isEmpty(backupDevices)) {
                    sendRenegotiateResult(renegotiateDTO.getClientId(), task, null, false, progress,
                            "任务【" + task.getTaskName() + "】没有可用的备选设备");
                    continue;
                }

                // 重新分配设备
                List<Map<String, Object>> allocatedDevices = reallocateDevicesForTask(
                        task, backupDevices, equipmentCapacity, equipmentUsage);

                if (CollUtil.isNotEmpty(allocatedDevices)) {
                    // 处理时间重叠
                    resolveTimeOverlaps(allocatedDevices);

                    // 更新设备使用记录
                    updateEquipmentUsage(allocatedDevices, equipmentUsage);

                    sendRenegotiateResult(renegotiateDTO.getClientId(), task, allocatedDevices, true, progress,
                            "任务【" + task.getTaskName() + "】重新分配成功，分配设备数量: " + allocatedDevices.size());
                } else {
                    sendRenegotiateResult(renegotiateDTO.getClientId(), task, null, false, progress,
                            "任务【" + task.getTaskName() + "】重新分配失败，没有可用设备");
                }

            } catch (Exception e) {
                log.error("任务 {} 重新分配时发生异常: {}", task.getId(), e.getMessage(), e);
                sendRenegotiateResult(renegotiateDTO.getClientId(), task, null, false, progress,
                        "任务【" + task.getTaskName() + "】重新分配异常: " + e.getMessage());
            }
        }

        log.info("重新协商完成");
    }

    /**
     * 转换旧参数结构为新参数结构（兼容性方法）
     */
    private RenegotiateDTO convertToRenegotiateDTO(ScheduleTaskDTO scheduleTaskDTO) {
        RenegotiateDTO renegotiateDTO = new RenegotiateDTO();
        renegotiateDTO.setClientId(scheduleTaskDTO.getClientId());
        renegotiateDTO.setFailedEquipments(scheduleTaskDTO.getFailedEquipments());
        renegotiateDTO.setSuccessEquipments(scheduleTaskDTO.getUsedEquipments());
        renegotiateDTO.setBackupEquipmentsGroup(scheduleTaskDTO.getBackupEquipmentsGroup());
        renegotiateDTO.setScheduleType(scheduleTaskDTO.getType());

        // 从备选设备组中提取任务ID
        if (scheduleTaskDTO.getBackupEquipmentsGroup() != null) {
            renegotiateDTO.setTaskIds(new ArrayList<>(scheduleTaskDTO.getBackupEquipmentsGroup().keySet()));
        }

        return renegotiateDTO;
    }

    /**
     * 从已成功分配的设备构建容量占用情况
     */
    private void buildCapacityFromSuccessEquipments(List<Map<String, Object>> successEquipments,
                                                   Map<String, Integer> equipmentCapacity,
                                                   Map<String, List<TimeSlot>> equipmentUsage) {
        if (CollUtil.isEmpty(successEquipments)) {
            return;
        }

        // 按设备ID分组
        Map<String, List<Map<String, Object>>> groupedByEquipment = successEquipments.stream()
                .collect(Collectors.groupingBy(e -> e.get("id").toString()));

        groupedByEquipment.forEach((equipmentId, equipmentTasks) -> {
            // 设置设备容量
            int targetCount = Integer.parseInt(equipmentTasks.get(0).get("targetCount").toString());
            equipmentCapacity.put(equipmentId, targetCount);

            // 添加已占用的时间段
            List<TimeSlot> usageList = new ArrayList<>();
            for (Map<String, Object> task : equipmentTasks) {
                try {
                    LocalDateTime entryTime = parseDateTime(task.get("entryTime").toString());
                    LocalDateTime exitTime = parseDateTime(task.get("exitTime").toString());
                    if (entryTime != null && exitTime != null) {
                        usageList.add(new TimeSlot(entryTime, exitTime));
                    }
                } catch (Exception e) {
                    log.warn("解析已成功设备 {} 的时间段失败: entryTime={}, exitTime={}",
                            equipmentId, task.get("entryTime"), task.get("exitTime"));
                }
            }
            equipmentUsage.put(equipmentId, usageList);
        });
    }

    /**
     * 释放失败设备的容量（实际上失败设备本来就没有占用容量，这里主要是日志记录）
     */
    private void releaseFailedEquipmentCapacity(List<Map<String, Object>> failedEquipments,
                                               Map<String, List<TimeSlot>> equipmentUsage) {
        if (CollUtil.isEmpty(failedEquipments)) {
            return;
        }

        for (Map<String, Object> failedEquipment : failedEquipments) {
            String equipmentId = failedEquipment.get("id").toString();
            log.info("设备 {} 分配失败，释放容量供其他任务使用", equipmentId);
            // 失败的设备本来就没有占用容量，这里主要是确保设备可以被重新分配
        }
    }

    /**
     * 为单个任务重新分配设备
     */
    private List<Map<String, Object>> reallocateDevicesForTask(RequirementTask task,
                                                               List<Map<String, Object>> backupDevices,
                                                               Map<String, Integer> equipmentCapacity,
                                                               Map<String, List<TimeSlot>> equipmentUsage) {
        List<Map<String, Object>> allocatedDevices = new ArrayList<>();

        // 1. 先尝试单个设备达到100%覆盖
        List<Map<String, Object>> singleDeviceCandidates = backupDevices.stream()
                .filter(device -> {
                    double coverRate = Double.parseDouble(device.get("coverRate").toString());
                    return coverRate >= 1.0; // 100%覆盖
                })
                .sorted((d1, d2) -> {
                    // 按覆盖率排序，覆盖率高的优先
                    double rate1 = Double.parseDouble(d1.get("coverRate").toString());
                    double rate2 = Double.parseDouble(d2.get("coverRate").toString());
                    return Double.compare(rate2, rate1);
                })
                .collect(Collectors.toList());

        // 2. 尝试分配单个100%覆盖的设备
        for (Map<String, Object> device : singleDeviceCandidates) {
            if (tryAllocateDeviceForRenegotiate(device, equipmentCapacity, equipmentUsage)) {
                allocatedDevices.add(device);
                // 标记其他设备为不需要
                markOtherDevicesAsUnused(backupDevices, device);
                return allocatedDevices;
            }
        }

        // 3. 如果没有单个设备能达到100%，尝试多设备组合
        return allocateMultipleDevicesForRenegotiate(task, backupDevices, equipmentCapacity, equipmentUsage);
    }

    /**
     * 尝试为重新协商分配单个设备
     */
    private boolean tryAllocateDeviceForRenegotiate(Map<String, Object> device,
                                                   Map<String, Integer> equipmentCapacity,
                                                   Map<String, List<TimeSlot>> equipmentUsage) {
        String equipmentId = device.get("id").toString();

        try {
            LocalDateTime entryTime = parseDateTime(device.get("entryTime").toString());
            LocalDateTime exitTime = parseDateTime(device.get("exitTime").toString());

            // 确保设备容量信息存在
            if (!equipmentCapacity.containsKey(equipmentId)) {
                int targetCount = Integer.parseInt(device.get("targetCount").toString());
                equipmentCapacity.put(equipmentId, targetCount);
                equipmentUsage.put(equipmentId, new ArrayList<>());
            }

            if (canAllocate(equipmentUsage.get(equipmentId), entryTime, exitTime, equipmentCapacity.get(equipmentId))) {
                // 分配成功，将 marked 修改为 1
                device.put("marked", 1);
                return true; // 注意：这里不立即添加到使用记录，等时间重叠处理完成后再添加
            }
        } catch (Exception e) {
            log.warn("尝试分配设备 {} 时发生异常: {}", equipmentId, e.getMessage());
        }

        return false;
    }

    /**
     * 多设备组合分配（重新协商版本）
     */
    private List<Map<String, Object>> allocateMultipleDevicesForRenegotiate(RequirementTask task,
                                                                            List<Map<String, Object>> backupDevices,
                                                                            Map<String, Integer> equipmentCapacity,
                                                                            Map<String, List<TimeSlot>> equipmentUsage) {
        List<Map<String, Object>> allocatedDevices = new ArrayList<>();

        // 按覆盖率排序设备
        List<Map<String, Object>> sortedDevices = backupDevices.stream()
                .sorted((d1, d2) -> {
                    double rate1 = Double.parseDouble(d1.get("coverRate").toString());
                    double rate2 = Double.parseDouble(d2.get("coverRate").toString());
                    return Double.compare(rate2, rate1); // 覆盖率高的优先
                })
                .collect(Collectors.toList());

        // 贪心算法：逐个添加设备直到达到100%覆盖或无法再添加
        double totalCoverage = 0.0;
        List<Polygon> allocatedPolygons = new ArrayList<>();

        for (Map<String, Object> device : sortedDevices) {
            if (tryAllocateDeviceForRenegotiate(device, equipmentCapacity, equipmentUsage)) {
                allocatedDevices.add(device);

                // 计算当前总覆盖率（需要考虑重叠）
                Polygon devicePolygon = (Polygon) device.get("polygon");
                if (devicePolygon != null) {
                    allocatedPolygons.add(devicePolygon);
                    totalCoverage = calculateCombinedCoverage(allocatedPolygons, task.getId());

                    // 如果达到100%覆盖，停止添加更多设备
                    if (totalCoverage >= 1.0) {
                        break;
                    }
                }
            }
        }

        // 将未分配的设备标记为0
        for (Map<String, Object> device : backupDevices) {
            if (!allocatedDevices.contains(device)) {
                device.put("marked", 0);
            }
        }

        return allocatedDevices;
    }

    /**
     * 更新设备使用记录
     */
    private void updateEquipmentUsage(List<Map<String, Object>> allocatedDevices,
                                     Map<String, List<TimeSlot>> equipmentUsage) {
        for (Map<String, Object> device : allocatedDevices) {
            if (device.get("marked").equals(1)) {
                String equipmentId = device.get("id").toString();
                try {
                    LocalDateTime entryTime = parseDateTime(device.get("entryTime").toString());
                    LocalDateTime exitTime = parseDateTime(device.get("exitTime").toString());
                    if (entryTime != null && exitTime != null) {
                        equipmentUsage.get(equipmentId).add(new TimeSlot(entryTime, exitTime));
                    }
                } catch (Exception e) {
                    log.warn("更新设备 {} 使用记录时发生异常: {}", equipmentId, e.getMessage());
                }
            }
        }
    }

    /**
     * 发送重新协商结果
     */
    private void sendRenegotiateResult(String clientId, RequirementTask task,
                                      List<Map<String, Object>> allocatedDevices,
                                      boolean success, double progress, String message) {
        try {
            // 构建结果对象
            Map<String, Object> result = new HashMap<>();
            result.put("taskId", task.getId());
            result.put("taskName", task.getTaskName());
            result.put("success", success);
            result.put("allocatedDevices", allocatedDevices);
            result.put("deviceCount", allocatedDevices != null ? allocatedDevices.size() : 0);

            ScheduleResultDTO.ScheduleResult scheduleResult = buildResult(result, success ? 1 : 0);
            ScheduleResultDTO scheduleResultDTO = buildScheduleResultDTO(scheduleResult, progress, message);

            // 发送WebSocket消息
            sendResult2Front(clientId, scheduleResultDTO);

            // 保存调度日志
            saveScheduleLog(scheduleResult, message, 2);

            log.info("任务 {} 重新协商结果已推送: {}", task.getId(), message);
        } catch (Exception e) {
            log.error("发送重新协商结果时发生异常: {}", e.getMessage(), e);
        }
    }

//    @Override
//    public void renegotiate(ScheduleTaskDTO scheduleTaskDTO) {
//        // 1. 构建“已使用设备”的容量与时间片
//        List<Map<String, Object>> usedEquipments = CollUtil.defaultIfEmpty(scheduleTaskDTO.getUsedEquipments(), new ArrayList<>());
//        Map<String, Integer> equipmentCapacity = new HashMap<>();
//        Map<String, List<TimeSlot>> equipmentUsage = new HashMap<>();
//
//        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
//
//        for (Map<String, Object> used : usedEquipments) {
//            String equipmentId = used.get("id") != null ? used.get("id").toString() : used.getOrDefault("equipmentId", "").toString();
//            if (StrUtil.isBlank(equipmentId)) continue;
//            int targetCount = used.get("targetCount") != null ? Integer.parseInt(used.get("targetCount").toString()) : equipmentCapacity.getOrDefault(equipmentId, 1);
//            equipmentCapacity.putIfAbsent(equipmentId, targetCount);
//
//            // 初始化使用时间片列表
//            equipmentUsage.computeIfAbsent(equipmentId, k -> new ArrayList<>());
//
//            // 添加已占用的时间片
//            Object eStart = used.get("entryTime");
//            Object eEnd = used.get("exitTime");
//            if (eStart != null && eEnd != null) {
//                LocalDateTime start = parseLdt(eStart.toString(), formatter);
//                LocalDateTime end = parseLdt(eEnd.toString(), formatter);
//                if (start != null && end != null && !end.isBefore(start)) {
//                    equipmentUsage.get(equipmentId).add(new TimeSlot(start, end));
//                }
//            }
//        }
//
//        // 2. 遍历“调度失败”的时间段，尝试从备选节点中补位（按遍历顺序），只取交集区间
//        List<Map<String, Object>> failedEquipments = CollUtil.defaultIfEmpty(scheduleTaskDTO.getFailedEquipments(), new ArrayList<>());
//        Map<String, List<Map<String, Object>>> backupEquipmentsGroup = scheduleTaskDTO.getBackupEquipmentsGroup();
//
//        int total = failedEquipments.size();
//        int idx = 0;
//        for (Map<String, Object> failed : failedEquipments) {
//            idx++;
//            String taskId = (String) failed.get("taskId");
//            String areaId = failed.get("areaId") != null ? failed.get("areaId").toString() : null;
//
//            LocalDateTime failStart = parseLdt(String.valueOf(failed.get("entryTime")), formatter);
//            LocalDateTime failEnd = parseLdt(String.valueOf(failed.get("exitTime")), formatter);
//            if (failStart == null || failEnd == null || failEnd.isBefore(failStart)) {
//                continue;
//            }
//
//            List<Map<String, Object>> backups = CollUtil.defaultIfEmpty(backupEquipmentsGroup.get(taskId), new ArrayList<>());
//
//            boolean success = false;
//            Map<String, Object> chosen = null;
//            LocalDateTime chosenStart = null;
//            LocalDateTime chosenEnd = null;
//
//            for (Map<String, Object> backup : backups) {
//                String equipId = backup.get("id") != null ? backup.get("id").toString() : backup.getOrDefault("equipmentId", "").toString();
//                if (StrUtil.isBlank(equipId)) continue;
//
//                // 初始化容量
//                int cap = equipmentCapacity.computeIfAbsent(equipId, k -> backup.get("targetCount") != null ? Integer.parseInt(backup.get("targetCount").toString()) : 1);
//                equipmentUsage.computeIfAbsent(equipId, k -> new ArrayList<>());
//
//                LocalDateTime bStart = parseLdt(String.valueOf(backup.get("entryTime")), formatter);
//                LocalDateTime bEnd = parseLdt(String.valueOf(backup.get("exitTime")), formatter);
//                if (bStart == null || bEnd == null) continue;
//
//                // 仅取交集
//                LocalDateTime interStart = failStart.isAfter(bStart) ? failStart : bStart;
//                LocalDateTime interEnd = failEnd.isBefore(bEnd) ? failEnd : bEnd;
//                if (interEnd == null || interStart == null || !interEnd.isAfter(interStart)) {
//                    continue; // 无交集
//                }
//
//                // 校验在交集区间内是否可分配（不超过 targetCount 并发）
//                if (canAllocate(equipmentUsage.get(equipId), interStart, interEnd, cap)) {
//                    chosen = new HashMap<>(backup);
//                    chosen.put("entryTime", interStart.format(formatter));
//                    chosen.put("exitTime", interEnd.format(formatter));
//                    chosen.put("taskId", taskId);
//                    if (areaId == null && backup.get("areaId") != null) areaId = backup.get("areaId").toString();
//                    success = true;
//
//                    // 更新设备使用时间片
//                    equipmentUsage.get(equipId).add(new TimeSlot(interStart, interEnd));
//                    break;
//                }
//            }
//
//            // 构建推送与日志
//            double progress = Double.parseDouble(String.format("%.2f", (idx * 100.0) / Math.max(total, 1)));
//
//            Map<String, Object> resultMap = new HashMap<>();
//            resultMap.put("taskId", taskId);
//            if (areaId != null) resultMap.put("areaId", areaId);
//
//            if (success && chosen != null) {
//                // 将备选设备信息合并进结果
//                resultMap.putAll(chosen);
//                // 统一 id 字段
//                if (!resultMap.containsKey("id") && resultMap.get("equipmentId") != null) {
//                    resultMap.put("id", resultMap.get("equipmentId"));
//                }
//            } else {
//                // 失败则把失败时间段也推送回去
//                resultMap.put("entryTime", failStart.format(formatter));
//                resultMap.put("exitTime", failEnd.format(formatter));
//            }
//
//            String message = buildMessage(resultMap, success ? 1 : 0);
//            ScheduleResultDTO.ScheduleResult scheduleResult = buildResult(resultMap, success ? 1 : 0);
//            ScheduleResultDTO scheduleResultDTO = buildScheduleResultDTO(scheduleResult, progress, message);
//
//            // 发送websocket给前端
//            sendResult2Front(scheduleTaskDTO.getClientId(), scheduleResultDTO);
//            // 保存调度信息（类型 2 二级协商）
//            saveScheduleLog(scheduleResult, message, 2);
//        }
//    }

    private LocalDateTime parseLdt(String timeStr, DateTimeFormatter formatter) {
        if (StrUtil.isBlank(timeStr) || timeStr.equals("null")) return null;
        try {
            return LocalDateTime.parse(timeStr, formatter);
        } catch (Exception e) {
            try {
                // 兼容 ISO 或其他格式
                return LocalDateTime.parse(timeStr);
            } catch (Exception ignore) {}
        }
        return null;
    }

    private ScheduleResultDTO.ScheduleResult buildResult(Map<String, Object> equipment, int schedule) {
        if (CollUtil.isEmpty(equipment)) return null;

        ScheduleResultDTO.ScheduleResult result = new ScheduleResultDTO.ScheduleResult();

        String taskId = (String) equipment.get("taskId");
        String areaId = (String) equipment.get("areaId");

        RequirementTask task = requirementTaskService.getById(taskId);
        DataArea dataArea = dataAreaService.getById(areaId);

        // 最终结果
        result.setAreaName(dataArea.getAreaName());
        result.setAreaId(areaId);


        result.setTaskName(task.getTaskName());
        result.setResult(schedule);
        result.setStartTime(task.getStartTime().toString());
        result.setEndTime(task.getEndTime().toString());
        result.setEntryTime(equipment != null ? equipment.get("entryTime").toString() : null);
        result.setExitTime(equipment != null ? equipment.get("exitTime").toString() : null);
        result.setTaskId(task.getId());
        result.setEquipmentId(equipment != null ? equipment.get("id").toString() : null);
        result.setEquipmentName(equipment != null ? equipment.get("name").toString() : null);
        result.setCoverRate(equipment != null ? Double.parseDouble(equipment.get("coverRate").toString()) : 0);
        result.setGeneralName(equipment != null ? equipment.get("generalName").toString() : null);
        result.setGeneralId(equipment != null ? equipment.get("generalId").toString() : null);

        return result;
    }

    private String buildMessage(Map<String, Object> equipment, int schedule) {
        String taskId = (String) equipment.get("taskId");
        RequirementTask task = requirementTaskService.getById(taskId);

        StringBuilder message = new StringBuilder();
        message.append("<")
                .append(task.getTaskName())
                .append("> ")
                .append(cn.hutool.core.date.DateUtil.now())
                .append(schedule == 1 ? " 调度成功" : " 调度失败");

        return message.toString();
    }

    private void sendResult2Front(String clientId, ScheduleResultDTO resultDTO) {
        WsMessageDTO wsMessageDTO = new WsMessageDTO();
        wsMessageDTO.setData(resultDTO);
        wsMessageDTO.setType(WebSocketTypeEnum.SCHEDULE.getCode());

        globalServer.sendMsg(clientId, JSON.toJSONString(wsMessageDTO));

        // 至少1-2秒推一次
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

    private LLAPredict getSatelliteCoordinate(String equipmentId, String generalId, RequirementTask task) {
        // 计算任务中间时间卫星的位置,以这个位置来计算卫星覆盖率
        Duration duration = Duration.between(task.getStartTime(), task.getEndTime());
        Duration half = duration.dividedBy(2);

        LocalDateTime middleDateTime = task.getStartTime().plus(half);


        SatelliteDTO satelliteDTO = new SatelliteDTO();
        satelliteDTO.setEquipmentId(equipmentId);
        satelliteDTO.setGeneraId(generalId);
        satelliteDTO.setDateTime(middleDateTime);

        List<LLAPredict> coordinate = satelliteService.getSatelliteCoordinate(satelliteDTO);

        return CollUtil.isNotEmpty(coordinate) ? coordinate.get(0) : null;
    }

    private void saveScheduleResult(Map<String, List<Map<String, Object>>> scheduledTask) {
        for (Map.Entry<String, List<Map<String, Object>>> entry : scheduledTask.entrySet()) {
            List<Map<String, Object>> equipments = entry.getValue();
            if (CollUtil.isEmpty(equipments)) {
                continue;
            }

            List<ConfirmScheduleDTO> params = new ArrayList<>();
            for (Map<String, Object> equipment : equipments) {
                // 构建存储数据结构
                ConfirmScheduleDTO scheduleDTO = new ConfirmScheduleDTO();
                scheduleDTO.setTaskId(entry.getKey());
                ConfirmScheduleDTO.EquipmentDTO equipmentDTO = new ConfirmScheduleDTO.EquipmentDTO();
                equipmentDTO.setGeneralId(equipment.get("generalId").toString());
                equipmentDTO.setEquipmentId(equipment.get("equipmentId").toString());
                equipmentDTO.setAreaId(equipment.get("areaId").toString());
                equipmentDTO.setCoverRate(BigDecimal.valueOf(Double.parseDouble(equipment.get("coverRate").toString())));

                params.add(scheduleDTO);
            }
            // 确认资源调度结果
            confirmSchedule(params);
        }
    }

    private Map<String, List<Map<String, Object>>> doSchedule(List<RequirementTaskDTO> tasks) {
        Map<String, List<Map<String, Object>>> scheduledTask = new HashMap<>();
        for (RequirementTaskDTO task : tasks) {
            AlgorithmDTO algorithmDTO = new AlgorithmDTO();
            algorithmDTO.setTaskIds(Collections.singletonList(task.getId()));
            // 默认用覆盖率优先调度算法
            algorithmDTO.setAlgo(AlgorithmContextEnum.SORTED_COVER.getCode());

            // 调用调度算法
            Map<String, List<Map<String, Object>>> result = invokeAlgorithm(algorithmDTO, null);

            log.info("任务：{}已完成调度， 调度结果为：{}", task.getTaskName(), result);

            List<Map<String, Object>> mergeResult = new ArrayList<>();

            // 汇总调度结果
            result.values().forEach(mergeResult::addAll);

            scheduledTask.put(task.getId(), mergeResult);
        }

        return scheduledTask;
    }

    private void changeTaskStatus(List<RequirementTaskDTO> tasks, TaskScheduleStatusEnum taskStatusEnum) {
        requirementTaskService.changeTaskStatus(tasks, taskStatusEnum);
    }

    private List<RequirementTaskDTO> queryWaitingScheduleTask() {
        List<RequirementTaskDTO> tasks = requirementTaskService.queryScheduleTask();

        return tasks.stream()
                .filter(t -> t.getStatus().equals(TaskScheduleStatusEnum.NOT_SCHEDULE.getCode()))
                .collect(Collectors.toList());
    }
}

// 辅助类：时间段表示
class TimeSlot {
    LocalDateTime start;
    LocalDateTime end;

    TimeSlot(LocalDateTime start, LocalDateTime end) {
        this.start = start;
        this.end = end;
    }

    boolean overlaps(LocalDateTime entryTime, LocalDateTime exitTime) {
        return !(entryTime.isAfter(end) || exitTime.isBefore(start));
    }
}
